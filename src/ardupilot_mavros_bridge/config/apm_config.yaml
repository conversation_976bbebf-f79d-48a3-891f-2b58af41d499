# 最小化ArduPilot配置文件 - 无编码器版本
# 只包含基本必要配置，避免插件冲突
#
/**:
  ros__parameters:
    startup_px4_usb_quirk: false

# 基本系统配置
/**/sys:
  ros__parameters:
    min_voltage: [10.0]
    disable_diag: false
    heartbeat_rate: 1.0
    conn_timeout: 10.0

# 时间同步
/**/time:
  ros__parameters:
    time_ref_source: "fcu"
    timesync_mode: MAVLINK
    timesync_rate: 10.0
    system_time_rate: 1.0

# 基本位置信息
/**/global_position:
  ros__parameters:
    frame_id: "map"
    child_frame_id: "base_link"
    tf.send: true
    tf.frame_id: "map"
    tf.child_frame_id: "base_link"

/**/local_position:
  ros__parameters:
    frame_id: "map"
    tf.send: true
    tf.frame_id: "map"
    tf.child_frame_id: "base_link"
    tf.send_fcu: false

# IMU数据
/**/imu:
  ros__parameters:
    frame_id: "base_link"

# 任务管理
/**/mission:
  ros__parameters:
    pull_after_gcs: true
    use_mission_item_int: true

