# ArduPilot配置文件 - 针对无人船/地面差速车优化
# 专为地面/水面车辆优化，减少不必要的计算和数据传输
#
/**:
  ros__parameters:
    startup_px4_usb_quirk: false

# === 系统配置 - 针对地面/水面车辆优化 ===
/**/sys:
  ros__parameters:
    min_voltage: [10.0]        # 最低电压告警 (适合12V系统)
    disable_diag: false        # 启用诊断
    heartbeat_rate: 2.0        # 心跳频率 2Hz (降低网络负载)
    heartbeat_mav_type: "GROUND_ROVER"  # 地面车辆类型
    conn_timeout: 15.0         # 连接超时 15秒 (地面车辆网络可能不稳定)

# === 时间同步 - 降低频率节省资源 ===
/**/time:
  ros__parameters:
    time_ref_source: "fcu"     # 使用飞控时间作为参考
    timesync_mode: MAVLINK     # MAVLink时间同步
    timesync_avg_alpha: 0.6    # 时间同步平均因子
    timesync_rate: 5.0         # 降低到5Hz (地面车辆不需要高频时间同步)
    system_time_rate: 0.5      # 系统时间发布频率0.5Hz

# === 全局位置配置 - 地面/水面车辆优化 ===
/**/global_position:
  ros__parameters:
    frame_id: "map"            # 全局坐标系
    child_frame_id: "base_link" # 车体坐标系
    rot_covariance: 99999.0    # 姿态协方差
    gps_uere: 1.0              # GPS用户等效距离误差
    use_relative_alt: true     # 使用相对高度
    tf.send: true              # 发送TF变换
    tf.frame_id: "map"
    tf.child_frame_id: "base_link"
    tf.rate_limit: 10.0        # 限制TF发布频率到10Hz

# === 本地位置配置 - 地面/水面车辆优化 ===
/**/local_position:
  ros__parameters:
    frame_id: "map"            # 本地坐标系
    tf.send: true              # 发送TF变换
    tf.frame_id: "map"
    tf.child_frame_id: "base_link"
    tf.send_fcu: false         # 不向飞控发送位置 (避免冲突)
    tf.rate_limit: 20.0        # 本地位置更新频率20Hz (导航需要)

# === IMU配置 - 地面/水面车辆优化 ===
/**/imu:
  ros__parameters:
    frame_id: "base_link"      # IMU安装在车体上
    linear_acceleration_stdev: 0.0003  # 线性加速度标准差
    angular_velocity_stdev: 0.02       # 角速度标准差
    orientation_stdev: 1.0             # 方向标准差
    magnetic_declination_auto: true    # 自动磁偏角
    magnetic_declination: 0.0          # 手动磁偏角 (如果不自动)

# === 任务管理 - 地面/水面车辆优化 ===
/**/mission:
  ros__parameters:
    pull_after_gcs: true       # GCS连接后拉取任务
    use_mission_item_int: true # 使用整数任务项 (更精确)

# === 距离传感器配置 - 避障用 ===
/**/distance_sensor:
  ros__parameters:
    rangefinder_pub:
      id: 0
      frame_id: "lidar"        # 激光雷达坐标系
      field_of_view: 0.0       # 视场角
      send_tf: false           # 不发送TF (由其他节点处理)
      sensor_position: {x: 0.0, y: 0.0, z: 0.1}  # 传感器位置
    rangefinder_sub:
      subscriber: true         # 启用订阅者
      id: 1
      orientation: PITCH_270   # 向下朝向 (地面检测)

# === 设定点配置 - 差速驱动优化 ===
/**/setpoint_velocity:
  ros__parameters:
    mav_frame: LOCAL_NED      # 使用本地NED坐标系

/**/setpoint_position:
  ros__parameters:
    mav_frame: LOCAL_NED      # 使用本地NED坐标系
    tf.listen: true           # 监听TF变换

# === 遥控配置 ===
/**/rc_io:
  ros__parameters:
    raw_rc: true              # 发布原始遥控数据

# === 参数管理 ===
/**/param:
  ros__parameters:
    use_comp_id_system_control: false  # 不使用系统控制组件ID

# === 命令接口 ===
/**/cmd:
  ros__parameters:
    use_comp_id_system_control: false   # 兼容老版本飞控

# === GPS状态 ===
/**/gps:
  ros__parameters:
    frame_id: "gps"           # GPS坐标系

# === 里程计 ===
/**/odometry:
  ros__parameters:
    fcu.odom_parent_id_des: "map"      # 里程计父坐标系
    fcu.odom_child_id_des: "base_link" # 里程计子坐标系
