# MAVROS配置文件 - 适用于ArduPilot (APM)
# 这个配置文件专门为ArduPilot飞控系统优化

# 连接配置
fcu_url: "udp://:14550@"  # ArduPilot默认UDP端口
gcs_url: ""               # 地面站连接URL (可选)
target_system_id: 1       # 目标系统ID
target_component_id: 1    # 目标组件ID
fcu_protocol: "v2.0"      # MAVLink协议版本

# 连接超时设置
conn:
  heartbeat_rate: 1.0     # 心跳包发送频率 (Hz)
  timeout: 10.0           # 连接超时时间 (秒)
  timesync_rate: 10.0     # 时间同步频率 (Hz)
  system_time_rate: 1.0   # 系统时间发送频率 (Hz)

# 时间同步配置
time:
  time_ref_source: "fcu"  # 时间参考源
  timesync_mode: MAVLINK  # 时间同步模式

# 启动配置
startup:
  px4_usb_quirk: false    # PX4 USB修复 (ArduPilot不需要)

# 插件配置
plugin_blacklist:
  # 禁用不需要的插件以减少资源消耗
  - 'safety_area'
  - 'trajectory'
  - 'vision_pose_estimate'
  - 'vision_speed_estimate'
  - 'vibration'
  - 'wheel_odometry'
  - 'wind_estimation'

plugin_whitelist: []

# 系统插件配置
sys:
  min_voltage: 10.0       # 最小电压告警
  disable_diag: false     # 启用诊断信息

# RC输入/输出配置
rc:
  in:
    # RC输入通道映射 (ArduPilot标准)
    channel_map:
      roll: 1      # 横滚通道
      pitch: 2     # 俯仰通道  
      throttle: 3  # 油门通道
      yaw: 4       # 偏航通道
      mode: 5      # 模式切换通道
      aux1: 6      # 辅助通道1
      aux2: 7      # 辅助通道2
      aux3: 8      # 辅助通道3
  
  out:
    # RC输出配置
    # 对于差速小车，我们主要关注通道1(左轮)和通道3(右轮)
    min_pwm: 1000    # 最小PWM值
    max_pwm: 2000    # 最大PWM值
    neutral_pwm: 1500 # 中性PWM值

# 本地位置配置
local_position:
  frame_id: "map"         # 坐标系ID
  tf:
    send: true            # 发送TF变换
    frame_id: "map"       # TF坐标系ID
    child_frame_id: "base_link"  # 子坐标系ID
    rate_limit: 50.0      # TF发送频率限制

# 全局位置配置  
global_position:
  frame_id: "map"         # 全局坐标系ID
  child_frame_id: "base_link"
  rot_covariance: 99999.0 # 旋转协方差
  gps_uere: 1.0          # GPS用户等效距离误差
  use_relative_alt: true  # 使用相对高度
  tf:
    send: true            # 发送全局位置TF
    frame_id: "map"
    global_frame_id: "earth"
    child_frame_id: "base_link"

# IMU配置
imu:
  frame_id: "base_link"   # IMU坐标系
  orientation_stdev: 0.0  # 方向标准差
  angular_velocity_stdev: 0.0  # 角速度标准差
  linear_acceleration_stdev: 0.0  # 线性加速度标准差
  magnetic_stdev: 0.0     # 磁力计标准差

# 设置点配置
setpoint_position:
  tf:
    listen: false         # 不监听TF
    frame_id: "map"
    child_frame_id: "target_position"
    rate_limit: 50.0

setpoint_velocity:
  mav_frame: LOCAL_NED    # MAVLink坐标系

# 参数配置
param:
  use_comp_id_system_control: false  # 不使用组件ID系统控制

# 命令配置
cmd:
  use_comp_id_system_control: false  # 不使用组件ID系统控制

# 距离传感器配置
distance_sensor:
  rangefinder_pub:
    id: 0
    frame_id: "rangefinder"
    orientation: PITCH_270  # 向下
    field_of_view: 0.0      # 视场角
    send_tf: true           # 发送TF
    sensor_position:
      x: 0.0
      y: 0.0  
      z: -0.1

# 地面真值配置
mocap:
  use_tf: false           # 不使用TF
  use_pose: false         # 不使用位姿

# 视觉里程计配置  
vision_pose:
  tf:
    listen: false         # 不监听视觉位姿TF
    frame_id: "odom"
    child_frame_id: "vision_estimate"
    rate_limit: 10.0

vision_speed:
  listen_twist: false     # 不监听速度

# 安全区域配置
safety_area:
  p1: [1.0, 1.0, 1.0]    # 安全区域点1
  p2: [-1.0, -1.0, -1.0] # 安全区域点2

# ArduPilot特定配置
# 这些参数对ArduPilot系统特别重要
apm:
  # 启用ArduPilot特定功能
  use_apm_mode: true      # 使用ArduPilot模式
  
  # 差速驱动配置
  skid_steering: true     # 启用滑移转向模式
  
  # 电机配置
  motor_config:
    # 对于差速小车，通常使用以下配置：
    # 通道1: 左侧电机
    # 通道3: 右侧电机  
    left_motor_channel: 1
    right_motor_channel: 3
    
  # 模式映射
  mode_mapping:
    manual: 0       # 手动模式
    learning: 2     # 学习模式  
    steering: 3     # 转向模式
    hold: 4         # 保持模式
    loiter: 5       # 悬停模式
    follow: 6       # 跟随模式
    simple: 7       # 简单模式
    auto: 10        # 自动模式
    rtl: 11         # 返回起点模式
    smart_rtl: 12   # 智能返回模式
    guided: 15      # 引导模式
    initialising: 16 # 初始化模式
