/**:
  ros__parameters:
    # 针对无人船/地面差速车优化的插件配置
    # 使用黑名单模式，禁用不需要的插件以节省系统资源
    plugin_denylist:
      # === 飞行相关插件 (地面/水面车辆不需要) ===
      - actuator_control      # 飞行器执行器控制
      - altitude             # 高度传感器 (地面车不需要精确高度)
      - landing_target       # 降落目标检测
      - optical_flow         # 光流传感器 (主要用于飞行器)
      - vision_speed         # 视觉速度估计
      - setpoint_accel       # 加速度设定点 (飞行器用)
      - setpoint_attitude    # 姿态设定点 (飞行器用)
      - setpoint_trajectory  # 轨迹设定点 (飞行器用)
      - trajectory           # 轨迹跟踪

      # === 高级导航插件 (基础导航不需要) ===
      - mocap_pose_estimate  # 动作捕捉位姿估计
      - vision_pose          # 视觉位姿估计
      - fake_gps             # 虚假GPS
      - gps_input            # GPS输入 (使用内置GPS即可)
      - gps_rtk              # RTK GPS (高精度应用才需要)

      # === 载荷和外设插件 ===
      - camera               # 相机控制
      - gimbal_control       # 云台控制
      - mount_control        # 挂载控制
      - play_tune            # 播放音调
      - tdr_radio            # TDR无线电
      - tunnel               # 数据隧道

      # === 高级传感器插件 ===
      - adsb                 # ADS-B 航空监视
      - cellular_status      # 蜂窝网络状态
      - esc_status           # 电调状态
      - esc_telemetry        # 电调遥测
      - mag_calibration_status # 磁力计校准状态
      - onboard_computer_status # 机载计算机状态
      - open_drone_id        # 开放无人机ID
      - wind_estimation      # 风力估计

      # === 调试和开发插件 ===
      - debug_value          # 调试值
      - ftp                  # 文件传输
      - hil                  # 硬件在环仿真
      - log_transfer         # 日志传输
      - vibration            # 振动分析

      # === 特殊应用插件 ===
      - cam_imu_sync         # 相机IMU同步
      - companion_process_status # 伴随进程状态
      - image_pub            # 图像发布
      - px4flow              # PX4Flow光流
      - wheel_odometry       # 轮式里程计 (我们用自己的实现)

      # === 保留的核心插件 ===
      # sys_status           # 系统状态 - 保留
      # sys_time             # 系统时间 - 保留
      # command              # 命令接口 - 保留
      # global_position      # 全局位置 - 保留
      # local_position       # 本地位置 - 保留
      # imu                  # IMU数据 - 保留
      # mission              # 任务管理 - 保留
      # waypoint             # 航点管理 - 保留
      # param                # 参数管理 - 保留
      # rc_io                # 遥控输入输出 - 保留
      # setpoint_position    # 位置设定点 - 保留 (导航用)
      # setpoint_velocity    # 速度设定点 - 保留 (差速控制用)
      # setpoint_raw         # 原始设定点 - 保留 (底层控制用)
      # manual_control       # 手动控制 - 保留
      # home_position        # 起始位置 - 保留
      # gps_status           # GPS状态 - 保留
      # nav_controller_output # 导航控制器输出 - 保留
      # vfr_hud              # VFR HUD显示 - 保留
      # distance_sensor      # 距离传感器 - 保留 (避障用)
      # rangefinder          # 测距仪 - 保留
      # obstacle_distance    # 障碍物距离 - 保留 (避障用)
      # odometry             # 里程计 - 保留
      # geofence             # 地理围栏 - 保留
      # rallypoint           # 集结点 - 保留
      # guided_target        # 引导目标 - 保留
      # terrain              # 地形数据 - 保留