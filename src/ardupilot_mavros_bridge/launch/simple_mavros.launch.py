#!/usr/bin/env python3
"""
简单的MAVROS启动文件
避免复杂配置，只使用基本参数
"""

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    """生成简单的MAVROS launch描述"""
    
    # 声明launch参数
    declare_fcu_url = DeclareLaunchArgument(
        'fcu_url',
        default_value='/dev/ttyACM0:921600',
        description='飞控连接URL'
    )
    
    declare_gcs_url = DeclareLaunchArgument(
        'gcs_url', 
        default_value='udp-b://@14550',
        description='地面站连接URL'
    )
    
    # MAVROS节点 - 最简配置
    mavros_node = Node(
        package='mavros',
        executable='mavros_node',
        name='mavros',
        output='screen',
        parameters=[{
            'fcu_url': LaunchConfiguration('fcu_url'),
            'gcs_url': LaunchConfiguration('gcs_url'),
        }]
    )
    
    # 创建launch描述
    ld = LaunchDescription()
    
    # 添加launch参数
    ld.add_action(declare_fcu_url)
    ld.add_action(declare_gcs_url)
    
    # 添加节点
    ld.add_action(mavros_node)
    
    return ld
