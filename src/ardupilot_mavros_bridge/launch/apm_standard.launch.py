#!/usr/bin/env python3
"""
ArduPilot MAVROS Launch文件 - 标准配置版本 (ROS2 Python格式)
使用系统默认配置，兼容性最好
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    """生成ArduPilot MAVROS launch描述 - 标准版本"""

    # 获取包路径
    ardupilot_mavros_bridge_share = FindPackageShare('ardupilot_mavros_bridge')
    mavros_share = FindPackageShare('mavros')

    # 声明launch参数
    declare_fcu_url = DeclareLaunchArgument(
        'fcu_url',
        default_value='/dev/ttyACM0:921600',
        description='飞控连接URL - 串口连接'
    )

    declare_gcs_url = DeclareLaunchArgument(
        'gcs_url',
        default_value='udp-b://@14550',
        description='地面站连接URL - UDP广播'
    )

    declare_tgt_system = DeclareLaunchArgument(
        'tgt_system',
        default_value='1',
        description='目标系统ID'
    )

    declare_tgt_component = DeclareLaunchArgument(
        'tgt_component',
        default_value='1',
        description='目标组件ID'
    )

    declare_fcu_protocol = DeclareLaunchArgument(
        'fcu_protocol',
        default_value='v2.0',
        description='MAVLink协议版本'
    )

    declare_namespace = DeclareLaunchArgument(
        'namespace',
        default_value='mavros',
        description='MAVROS命名空间'
    )

    declare_log_output = DeclareLaunchArgument(
        'log_output',
        default_value='screen',
        description='日志输出方式'
    )

    declare_respawn_mavros = DeclareLaunchArgument(
        'respawn_mavros',
        default_value='false',
        description='是否自动重启MAVROS'
    )

    # 配置文件路径 - 使用系统默认配置
    pluginlists_yaml = PathJoinSubstitution([
        mavros_share,
        'launch',
        'apm_pluginlists.yaml'
    ])

    config_yaml = PathJoinSubstitution([
        mavros_share,
        'launch',
        'apm_config.yaml'
    ])

    # 包含通用的mavros节点launch文件
    mavros_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                ardupilot_mavros_bridge_share,
                'launch',
                'mavros_node.launch.py'
            ])
        ]),
        launch_arguments={
            'fcu_url': LaunchConfiguration('fcu_url'),
            'gcs_url': LaunchConfiguration('gcs_url'),
            'tgt_system': LaunchConfiguration('tgt_system'),
            'tgt_component': LaunchConfiguration('tgt_component'),
            'fcu_protocol': LaunchConfiguration('fcu_protocol'),
            'namespace': LaunchConfiguration('namespace'),
            'log_output': LaunchConfiguration('log_output'),
            'respawn_mavros': LaunchConfiguration('respawn_mavros'),
            'pluginlists_yaml': pluginlists_yaml,
            'config_yaml': config_yaml,
        }.items()
    )

    # 创建launch描述
    ld = LaunchDescription()

    # 添加launch参数声明
    ld.add_action(declare_fcu_url)
    ld.add_action(declare_gcs_url)
    ld.add_action(declare_tgt_system)
    ld.add_action(declare_tgt_component)
    ld.add_action(declare_fcu_protocol)
    ld.add_action(declare_namespace)
    ld.add_action(declare_log_output)
    ld.add_action(declare_respawn_mavros)

    # 添加MAVROS launch
    ld.add_action(mavros_launch)

    return ld


if __name__ == '__main__':
    generate_launch_description()
