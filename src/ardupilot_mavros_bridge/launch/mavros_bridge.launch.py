#!/usr/bin/env python3
"""
MAVROS桥接Launch文件 - 完整系统启动
包含XML格式的MAVROS launch文件和PWM到cmd_vel桥接节点
专为无人船/地面差速车优化
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import AnyLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """生成launch描述"""
    
    # 获取包路径
    pkg_share = FindPackageShare('ardupilot_mavros_bridge')

    # === MAVROS相关参数 ===
    declare_fcu_url = DeclareLaunchArgument(
        'fcu_url',
        default_value='/dev/ttyACM0:921600',  # 串口连接飞控
        description='FCU连接URL - 串口'
    )

    declare_gcs_url = DeclareLaunchArgument(
        'gcs_url',
        default_value='udp-b://@14550',  # UDP广播给GCS
        description='地面站连接URL - UDP广播'
    )

    declare_tgt_system = DeclareLaunchArgument(
        'tgt_system',
        default_value='1',
        description='目标系统ID'
    )

    declare_tgt_component = DeclareLaunchArgument(
        'tgt_component',
        default_value='1',
        description='目标组件ID'
    )

    declare_fcu_protocol = DeclareLaunchArgument(
        'fcu_protocol',
        default_value='v2.0',
        description='MAVLink协议版本'
    )

    declare_mavros_namespace = DeclareLaunchArgument(
        'mavros_namespace',
        default_value='mavros',
        description='MAVROS命名空间'
    )

    declare_use_optimized_config = DeclareLaunchArgument(
        'use_optimized_config',
        default_value='true',
        description='是否使用优化配置 (针对地面/水面车辆)'
    )

    # === 系统参数 ===
    declare_log_level = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='日志级别 (debug, info, warn, error)'
    )

    declare_namespace = DeclareLaunchArgument(
        'namespace',
        default_value='',
        description='节点命名空间'
    )

    declare_use_sim_time = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='是否使用仿真时间'
    )

    # === Bridge节点参数 ===
    declare_left_motor_channel = DeclareLaunchArgument(
        'left_motor_channel',
        default_value='1',
        description='左电机通道号 (1-based)'
    )

    declare_right_motor_channel = DeclareLaunchArgument(
        'right_motor_channel',
        default_value='3',
        description='右电机通道号 (1-based)'
    )

    declare_wheelbase = DeclareLaunchArgument(
        'wheelbase',
        default_value='0.4',
        description='轮距 (米)'
    )

    declare_max_linear_speed = DeclareLaunchArgument(
        'max_linear_speed',
        default_value='2.0',
        description='最大线速度 (m/s)'
    )

    declare_max_angular_speed = DeclareLaunchArgument(
        'max_angular_speed',
        default_value='2.0',
        description='最大角速度 (rad/s)'
    )

    declare_cmd_vel_topic = DeclareLaunchArgument(
        'cmd_vel_topic',
        default_value='/cmd_vel',
        description='cmd_vel话题名称'
    )

    declare_enable_safety = DeclareLaunchArgument(
        'enable_safety',
        default_value='true',
        description='是否启用安全检查'
    )

    declare_enable_bridge = DeclareLaunchArgument(
        'enable_bridge',
        default_value='true',
        description='是否启动桥接节点'
    )

    # === 包含XML格式的MAVROS launch文件 ===
    # 根据配置选择标准或优化版本
    mavros_launch_file = PathJoinSubstitution([
        pkg_share,
        'launch',
        ['apm_optimized.launch']  # 默认使用优化配置
    ])

    # 包含MAVROS XML launch文件
    mavros_launch = IncludeLaunchDescription(
        AnyLaunchDescriptionSource(mavros_launch_file),
        launch_arguments={
            'fcu_url': LaunchConfiguration('fcu_url'),
            'gcs_url': LaunchConfiguration('gcs_url'),
            'tgt_system': LaunchConfiguration('tgt_system'),
            'tgt_component': LaunchConfiguration('tgt_component'),
            'fcu_protocol': LaunchConfiguration('fcu_protocol'),
            'namespace': LaunchConfiguration('mavros_namespace'),
        }.items()
    )

    # === MAVROS Bridge节点 ===
    bridge_node = Node(
        package='ardupilot_mavros_bridge',
        executable='mavros_bridge_node',
        name='mavros_bridge',
        namespace=LaunchConfiguration('namespace'),
        output='screen',
        parameters=[{
            'left_motor_channel': LaunchConfiguration('left_motor_channel'),
            'right_motor_channel': LaunchConfiguration('right_motor_channel'),
            'wheelbase': LaunchConfiguration('wheelbase'),
            'max_linear_speed': LaunchConfiguration('max_linear_speed'),
            'max_angular_speed': LaunchConfiguration('max_angular_speed'),
            'cmd_vel_topic': LaunchConfiguration('cmd_vel_topic'),
            'enable_safety_check': LaunchConfiguration('enable_safety'),
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'rc_out_topic': '/mavros/rc/out',
        }],
        condition=IfCondition(LaunchConfiguration('enable_bridge')),
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )

    # === 创建launch描述 ===
    ld = LaunchDescription()

    # 添加MAVROS相关参数
    ld.add_action(declare_fcu_url)
    ld.add_action(declare_gcs_url)
    ld.add_action(declare_tgt_system)
    ld.add_action(declare_tgt_component)
    ld.add_action(declare_fcu_protocol)
    ld.add_action(declare_mavros_namespace)
    ld.add_action(declare_use_optimized_config)

    # 添加系统参数
    ld.add_action(declare_log_level)
    ld.add_action(declare_namespace)
    ld.add_action(declare_use_sim_time)

    # 添加Bridge节点参数
    ld.add_action(declare_left_motor_channel)
    ld.add_action(declare_right_motor_channel)
    ld.add_action(declare_wheelbase)
    ld.add_action(declare_max_linear_speed)
    ld.add_action(declare_max_angular_speed)
    ld.add_action(declare_cmd_vel_topic)
    ld.add_action(declare_enable_safety)
    ld.add_action(declare_enable_bridge)

    # 添加MAVROS launch (XML格式)
    ld.add_action(mavros_launch)

    # 添加Bridge节点
    ld.add_action(bridge_node)

    return ld
