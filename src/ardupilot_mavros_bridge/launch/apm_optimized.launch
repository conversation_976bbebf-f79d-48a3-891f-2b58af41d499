<launch>
	<!-- vim: set ft=xml noet : -->
	<!-- ArduPilot MAVROS Launch文件 - 无人船/地面差速车优化版本 -->
	<!-- 使用优化的插件配置，减少系统资源使用 -->

	<arg name="fcu_url" default="/dev/ttyACM0:921600" />
	<arg name="gcs_url" default="udp-b://@14550" />
	<arg name="tgt_system" default="1" />
	<arg name="tgt_component" default="1" />
	<arg name="log_output" default="screen" />
	<arg name="fcu_protocol" default="v2.0" />
	<arg name="respawn_mavros" default="false" />
	<arg name="namespace" default="mavros"/>
	
	<!-- 包含官方的node.launch文件，使用优化配置 -->
	<include file="$(find-pkg-share mavros)/launch/node.launch">
		<!-- 使用优化的插件列表 - 只加载地面/水面车辆需要的插件 -->
		<arg name="pluginlists_yaml" value="$(find-pkg-share ardupilot_mavros_bridge)/config/apm_pluginlists.yaml" />
		<!-- 使用优化的配置文件 - 针对地面/水面车辆调优 -->
		<arg name="config_yaml" value="$(find-pkg-share ardupilot_mavros_bridge)/config/apm_config_optimized.yaml" />
		<arg name="fcu_url" value="$(var fcu_url)" />
		<arg name="gcs_url" value="$(var gcs_url)" />
		<arg name="tgt_system" value="$(var tgt_system)" />
		<arg name="tgt_component" value="$(var tgt_component)" />
		<arg name="fcu_protocol" value="$(var fcu_protocol)" />
		<arg name="respawn_mavros" value="$(var respawn_mavros)" />
		<arg name="namespace" value="$(var namespace)"/>
	</include>
</launch>
