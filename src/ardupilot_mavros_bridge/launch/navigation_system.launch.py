#!/usr/bin/env python3
"""
完整导航系统Launch文件
集成ArduPilot MAVROS + Navigation2的分层导航架构
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, GroupAction
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node, PushRosNamespace
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """生成完整导航系统launch描述"""
    
    # 获取包路径
    pkg_share = FindPackageShare('ardupilot_mavros_bridge')
    nav2_bringup_dir = FindPackageShare('nav2_bringup')
    
    # 声明launch参数
    declare_use_sim_time = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='是否使用仿真时间'
    )
    
    declare_map_yaml = DeclareLaunchArgument(
        'map',
        default_value='',
        description='地图文件路径 (可选，用于已知环境)'
    )
    
    declare_params_file = DeclareLaunchArgument(
        'params_file',
        default_value=PathJoinSubstitution([pkg_share, 'config', 'nav2_params.yaml']),
        description='Navigation2参数文件'
    )
    
    declare_autostart = DeclareLaunchArgument(
        'autostart',
        default_value='true',
        description='自动启动Navigation2节点'
    )
    
    declare_use_composition = DeclareLaunchArgument(
        'use_composition',
        default_value='True',
        description='是否使用组合节点'
    )
    
    # ArduPilot MAVROS系统
    mavros_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([pkg_share, 'launch', 'complete_system.launch.py'])
        ]),
        launch_arguments={
            'enable_mission_control': 'true',
            'enable_navigation2': 'true',
        }.items()
    )
    
    # Navigation2 Bringup
    nav2_bringup_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([nav2_bringup_dir, 'launch', 'bringup_launch.py'])
        ]),
        launch_arguments={
            'map': LaunchConfiguration('map'),
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'params_file': LaunchConfiguration('params_file'),
            'autostart': LaunchConfiguration('autostart'),
            'use_composition': LaunchConfiguration('use_composition'),
        }.items()
    )
    
    # 激光雷达节点 (示例，根据实际硬件调整)
    lidar_node = Node(
        package='rplidar_ros',
        executable='rplidar_composition',
        name='rplidar_node',
        output='screen',
        parameters=[{
            'serial_port': '/dev/ttyUSB0',
            'serial_baudrate': 115200,
            'frame_id': 'laser',
            'inverted': False,
            'angle_compensate': True,
        }],
        condition=IfCondition('true')  # 可以添加条件控制
    )
    
    # 静态变换发布器 (激光雷达到base_link)
    laser_tf_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='laser_tf_publisher',
        arguments=['0', '0', '0.1', '0', '0', '0', 'base_link', 'laser'],
        output='screen'
    )
    
    # 里程计变换发布器 (如果需要)
    odom_tf_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='odom_tf_publisher',
        arguments=['0', '0', '0', '0', '0', '0', 'map', 'odom'],
        output='screen'
    )
    
    # RViz可视化 (可选)
    rviz_config_file = PathJoinSubstitution([
        nav2_bringup_dir, 'rviz', 'nav2_default_view.rviz'
    ])
    
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        arguments=['-d', rviz_config_file],
        parameters=[{'use_sim_time': LaunchConfiguration('use_sim_time')}],
        output='screen',
        condition=IfCondition('false')  # 默认不启动，可以手动设置为true
    )
    
    # 创建launch描述
    ld = LaunchDescription()
    
    # 添加launch参数
    ld.add_action(declare_use_sim_time)
    ld.add_action(declare_map_yaml)
    ld.add_action(declare_params_file)
    ld.add_action(declare_autostart)
    ld.add_action(declare_use_composition)
    
    # 添加节点和launch文件
    ld.add_action(mavros_launch)
    ld.add_action(nav2_bringup_launch)
    ld.add_action(lidar_node)
    ld.add_action(laser_tf_node)
    ld.add_action(odom_tf_node)
    ld.add_action(rviz_node)
    
    return ld
