#!/usr/bin/env python3
"""
MAVROS节点Launch文件 - ROS2 Python格式
相当于官方的node.launch，但使用Python格式提供更好的灵活性
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    """生成MAVROS节点launch描述 - 通用版本"""

    # 声明所有必需的launch参数
    declare_fcu_url = DeclareLaunchArgument(
        'fcu_url',
        description='飞控连接URL'
    )

    declare_gcs_url = DeclareLaunchArgument(
        'gcs_url',
        description='地面站连接URL'
    )

    declare_tgt_system = DeclareLaunchArgument(
        'tgt_system',
        description='目标系统ID'
    )

    declare_tgt_component = DeclareLaunchArgument(
        'tgt_component',
        description='目标组件ID'
    )

    declare_pluginlists_yaml = DeclareLaunchArgument(
        'pluginlists_yaml',
        description='插件列表YAML文件路径'
    )

    declare_config_yaml = DeclareLaunchArgument(
        'config_yaml',
        description='配置YAML文件路径'
    )

    declare_log_output = DeclareLaunchArgument(
        'log_output',
        default_value='screen',
        description='日志输出方式'
    )

    declare_fcu_protocol = DeclareLaunchArgument(
        'fcu_protocol',
        default_value='v2.0',
        description='MAVLink协议版本'
    )

    declare_respawn_mavros = DeclareLaunchArgument(
        'respawn_mavros',
        default_value='false',
        description='是否自动重启MAVROS'
    )

    declare_namespace = DeclareLaunchArgument(
        'namespace',
        default_value='mavros',
        description='MAVROS命名空间'
    )

    # MAVROS节点
    mavros_node = Node(
        package='mavros',
        executable='mavros_node',
        name='mavros',  # 使用标准名称
        namespace=LaunchConfiguration('namespace'),
        output=LaunchConfiguration('log_output'),
        parameters=[
            # 从参数文件加载配置
            LaunchConfiguration('pluginlists_yaml'),
            LaunchConfiguration('config_yaml'),
            # 连接参数
            {
                'fcu_url': LaunchConfiguration('fcu_url'),
                'gcs_url': LaunchConfiguration('gcs_url'),
                'tgt_system': LaunchConfiguration('tgt_system'),
                'tgt_component': LaunchConfiguration('tgt_component'),
                'fcu_protocol': LaunchConfiguration('fcu_protocol'),
            }
        ],
        respawn=LaunchConfiguration('respawn_mavros'),
        respawn_delay=2.0,
    )

    # 创建launch描述
    ld = LaunchDescription()

    # 添加所有参数声明
    ld.add_action(declare_fcu_url)
    ld.add_action(declare_gcs_url)
    ld.add_action(declare_tgt_system)
    ld.add_action(declare_tgt_component)
    ld.add_action(declare_pluginlists_yaml)
    ld.add_action(declare_config_yaml)
    ld.add_action(declare_log_output)
    ld.add_action(declare_fcu_protocol)
    ld.add_action(declare_respawn_mavros)
    ld.add_action(declare_namespace)

    # 添加MAVROS节点
    ld.add_action(mavros_node)

    return ld


if __name__ == '__main__':
    generate_launch_description()
