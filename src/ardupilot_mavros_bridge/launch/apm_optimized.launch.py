#!/usr/bin/env python3
"""
ArduPilot MAVROS Launch文件 - 无人船/地面差速车优化版本 (ROS2 Python格式)
使用优化的插件配置，减少系统资源使用
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    """生成ArduPilot MAVROS launch描述 - 优化版本"""

    # 获取包路径
    ardupilot_mavros_bridge_share = FindPackageShare('ardupilot_mavros_bridge')

    # 声明launch参数
    declare_fcu_url = DeclareLaunchArgument(
        'fcu_url',
        default_value='/dev/ttyACM0:921600',
        description='飞控连接URL - 串口连接'
    )

    declare_gcs_url = DeclareLaunchArgument(
        'gcs_url',
        default_value='udp-b://@14550',
        description='地面站连接URL - UDP广播'
    )

    declare_tgt_system = DeclareLaunchArgument(
        'tgt_system',
        default_value='1',
        description='目标系统ID'
    )

    declare_tgt_component = DeclareLaunchArgument(
        'tgt_component',
        default_value='1',
        description='目标组件ID'
    )

    declare_fcu_protocol = DeclareLaunchArgument(
        'fcu_protocol',
        default_value='v2.0',
        description='MAVLink协议版本'
    )

    declare_namespace = DeclareLaunchArgument(
        'namespace',
        default_value='mavros',
        description='MAVROS命名空间'
    )

    declare_log_output = DeclareLaunchArgument(
        'log_output',
        default_value='screen',
        description='日志输出方式'
    )

    declare_respawn_mavros = DeclareLaunchArgument(
        'respawn_mavros',
        default_value='false',
        description='是否自动重启MAVROS'
    )

    # 配置文件路径 - 使用优化配置
    pluginlists_yaml = PathJoinSubstitution([
        ardupilot_mavros_bridge_share,
        'config',
        'apm_pluginlists.yaml'
    ])

    config_yaml = PathJoinSubstitution([
        ardupilot_mavros_bridge_share,
        'config',
        'apm_config_optimized.yaml'
    ])

    # MAVROS节点 - 使用优化配置
    mavros_node = Node(
        package='mavros',
        executable='mavros_node',
        name='mavros',  # 使用标准名称，避免冲突
        namespace=LaunchConfiguration('namespace'),
        output=LaunchConfiguration('log_output'),
        parameters=[
            # 加载插件黑名单配置
            pluginlists_yaml,
            # 加载优化的配置文件
            config_yaml,
            # 连接参数
            {
                'fcu_url': LaunchConfiguration('fcu_url'),
                'gcs_url': LaunchConfiguration('gcs_url'),
                'tgt_system': LaunchConfiguration('tgt_system'),
                'tgt_component': LaunchConfiguration('tgt_component'),
                'fcu_protocol': LaunchConfiguration('fcu_protocol'),
            }
        ],
        arguments=['--ros-args', '--log-level', 'info'],
        respawn=LaunchConfiguration('respawn_mavros'),
        respawn_delay=2.0,  # 重启延迟2秒
    )

    # 创建launch描述
    ld = LaunchDescription()

    # 添加launch参数声明
    ld.add_action(declare_fcu_url)
    ld.add_action(declare_gcs_url)
    ld.add_action(declare_tgt_system)
    ld.add_action(declare_tgt_component)
    ld.add_action(declare_fcu_protocol)
    ld.add_action(declare_namespace)
    ld.add_action(declare_log_output)
    ld.add_action(declare_respawn_mavros)

    # 添加MAVROS节点
    ld.add_action(mavros_node)

    return ld


if __name__ == '__main__':
    generate_launch_description()
