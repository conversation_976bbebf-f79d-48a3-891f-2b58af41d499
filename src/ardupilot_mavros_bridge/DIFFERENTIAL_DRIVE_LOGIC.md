# 差速驱动逻辑说明

## 概述

本文档详细说明了ArduPilot PWM输出到ROS cmd_vel话题的正确转换逻辑，基于差速驱动运动学模型，直接将PWM信号转换为车辆的线速度和角速度。

## 硬件连接理解

### ArduPilot PWM输出
```
通道1 (SERVO1) → 左电机 ESC
通道3 (SERVO3) → 右电机 ESC
```

### ArduPilot配置
```
SERVO1_FUNCTION = 73  # ThrottleLeft (左电机)
SERVO3_FUNCTION = 74  # ThrottleRight (右电机)
FRAME_CLASS = 1       # Rover
FRAME_TYPE = 1        # Skid Steering (滑移转向)
```

## 错误的理解 ❌

**之前错误的逻辑**：
- 通道1 = "油门" (throttle)
- 通道3 = "转向" (steering)
- 转换公式：
  ```python
  linear_vel = throttle_normalized * max_speed
  angular_vel = steering_normalized * max_angular_speed
  ```

这种理解是**完全错误**的，因为：
1. ArduPilot的PWM输出直接控制左右电机
2. 不存在"油门"和"转向"的概念
3. 需要使用差速驱动运动学进行转换

## 正确的差速驱动逻辑 ✅

### 基于已知车辆最大速度的转换方法

我们直接基于车辆已知的最大线速度和最大角速度进行转换，不需要考虑电机转速、车轮半径等物理细节。

```python
def differential_drive_kinematics(left_pwm, right_pwm):
    # 1. PWM归一化 (1000-2000 → -1.0到1.0)
    left_normalized = normalize_pwm(left_pwm)
    right_normalized = normalize_pwm(right_pwm)

    # 2. 应用死区
    left_normalized = apply_deadzone(left_normalized)
    right_normalized = apply_deadzone(right_normalized)

    # 3. 基于差速驱动运动学的直接转换
    # 线速度 = 两侧归一化值的平均 × 最大线速度
    linear_vel = (left_normalized + right_normalized) / 2.0 * max_linear_speed

    # 角速度 = 归一化值差 × 最大角速度
    angular_vel = (right_normalized - left_normalized) * max_angular_speed

    return linear_vel, angular_vel
```

### 转换原理说明

1. **线速度计算**：
   - 当左右PWM相同时，车辆直线运动
   - 线速度 = 左右归一化值的平均值 × 最大线速度
   - 这确保了当两侧都是最大PWM时，车辆以最大线速度前进

2. **角速度计算**：
   - 当左右PWM不同时，产生转向
   - 角速度 = (右侧 - 左侧)归一化值 × 最大角速度
   - 这确保了最大PWM差时，车辆以最大角速度转向

## 运动学原理

### 线速度计算
```
v = (v_left + v_right) / 2
```
- 车辆的线速度等于左右轮速度的平均值
- 当左右轮速度相同时，车辆直线行驶

### 角速度计算
```
ω = (v_right - v_left) / L
```
其中：
- `ω` = 角速度 (rad/s)
- `v_right` = 右轮线速度 (m/s)
- `v_left` = 左轮线速度 (m/s)  
- `L` = 轮距 (wheelbase, m)

### 运动模式分析

假设最大线速度 = 2.0 m/s，最大角速度 = 2.0 rad/s

| 左PWM | 右PWM | 左归一化 | 右归一化 | 线速度 | 角速度 | 运动状态 |
|-------|-------|----------|----------|--------|--------|----------|
| 1600  | 1600  | +0.2     | +0.2     | +0.4   | 0      | 慢速前进 |
| 2000  | 2000  | +1.0     | +1.0     | +2.0   | 0      | 最大速度前进 |
| 1000  | 1000  | -1.0     | -1.0     | -2.0   | 0      | 最大速度后退 |
| 2000  | 1500  | +1.0     | 0        | +1.0   | -2.0   | 前进左转 |
| 1500  | 2000  | 0        | +1.0     | +1.0   | +2.0   | 前进右转 |
| 2000  | 1000  | +1.0     | -1.0     | 0      | -4.0   | 原地左转* |
| 1000  | 2000  | -1.0     | +1.0     | 0      | +4.0   | 原地右转* |

*注：原地转向时角速度会超过设定的最大值，会被限制到±2.0 rad/s*

## 参数配置

### 关键参数说明

```yaml
# 电机通道配置
left_motor_channel: 1    # 左电机通道 (SERVO1)
right_motor_channel: 3   # 右电机通道 (SERVO3)

# 车辆物理参数
wheelbase: 0.4          # 轮距 (m) - 仅用于文档说明，实际转换中不使用

# PWM范围
pwm_min: 1000          # PWM最小值
pwm_max: 2000          # PWM最大值
pwm_neutral: 1500      # PWM中性值
pwm_deadzone: 50       # PWM死区

# 车辆速度能力 (关键参数)
max_linear_speed: 2.0  # 车辆最大线速度 (m/s) - 根据实际测试设定
max_angular_speed: 2.0 # 车辆最大角速度 (rad/s) - 根据实际测试设定
```

### 参数调整指南

1. **轮距 (wheelbase)**：
   - 测量左右轮中心的距离
   - 影响转向灵敏度
   - 轮距越大，相同速度差产生的角速度越小

2. **最大电机速度 (max_motor_speed)**：
   - 根据实际电机和车轮规格设定
   - 计算公式：`max_motor_speed = max_rpm * wheel_circumference / 60`

3. **PWM死区 (pwm_deadzone)**：
   - 消除PWM信号噪声
   - 通常设置为总PWM范围的2-5%

## 实际应用示例

### 示例1：直线行驶
```
输入：left_pwm=1600, right_pwm=1600
处理：
  left_speed = (1600-1500)/500 * 2.0 = 0.4 m/s
  right_speed = (1600-1500)/500 * 2.0 = 0.4 m/s
输出：
  linear_vel = (0.4 + 0.4) / 2 = 0.4 m/s
  angular_vel = (0.4 - 0.4) / 0.4 = 0 rad/s
结果：以0.4m/s直线前进
```

### 示例2：右转
```
输入：left_pwm=1700, right_pwm=1300  
处理：
  left_speed = (1700-1500)/500 * 2.0 = 0.8 m/s
  right_speed = (1300-1500)/500 * 2.0 = -0.8 m/s
输出：
  linear_vel = (0.8 + (-0.8)) / 2 = 0 m/s
  angular_vel = (-0.8 - 0.8) / 0.4 = -4 rad/s  
结果：原地右转，角速度4 rad/s
```

## 调试和验证

### 1. 检查PWM输入
```bash
ros2 topic echo /mavros/rc/out
```

### 2. 检查转换输出
```bash
ros2 topic echo /cmd_vel
```

### 3. 验证运动学
使用以下测试序列验证转换正确性：
1. 相同PWM → 直线运动 (angular_vel ≈ 0)
2. 左高右低 → 左转 (angular_vel < 0)  
3. 左低右高 → 右转 (angular_vel > 0)
4. 反向PWM → 原地转向 (linear_vel ≈ 0)

## 常见问题

### Q: 为什么使用通道1和3而不是1和2？
A: 这是ArduPilot Rover的标准配置，通道2通常用于其他功能。

### Q: 如何确定轮距参数？
A: 物理测量左右轮中心的距离，或通过实际测试调整到合适的转向响应。

### Q: 角速度方向如何确定？
A: 按照ROS标准，正角速度为逆时针旋转，负角速度为顺时针旋转。

### Q: 如何处理PWM信号噪声？
A: 通过调整pwm_deadzone参数，过滤小幅度的PWM变化。
