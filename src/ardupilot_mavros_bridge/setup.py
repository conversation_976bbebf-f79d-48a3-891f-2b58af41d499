from setuptools import find_packages, setup

package_name = 'ardupilot_mavros_bridge'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', [
            'launch/apm.launch',
            'launch/apm_optimized.launch',
            'launch/apm.launch.py',
            'launch/simple_mavros.launch.py',
            'launch/mavros_bridge.launch.py',
            'launch/complete_system.launch.py',
            'launch/navigation_system.launch.py'
        ]),
        ('share/' + package_name + '/config', [
            'config/mavros_apm.yaml',
            'config/apm_config.yaml',
            'config/apm_config_optimized.yaml',
            'config/apm_pluginlists.yaml',
            'config/nav2_params.yaml'
        ]),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='arduonboard',
    maintainer_email='<EMAIL>',
    description='ArduPilot MAVROS Bridge for differential drive robots',
    license='MIT',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'mavros_bridge_node = ardupilot_mavros_bridge.mavros_bridge_node:main',
            'mission_control_node = ardupilot_mavros_bridge.mission_control_node:main',
        ],
    },
)
