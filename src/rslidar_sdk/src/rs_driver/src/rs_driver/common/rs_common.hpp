/*********************************************************************************************************************
Copyright (c) 2020 RoboSense
All rights reserved

By downloading, copying, installing or using the software you agree to this license. If you do not agree to this
license, do not download, install, copy or use the software.

License Agreement
For RoboSense LiDAR SDK Library
(3-clause BSD License)

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the
following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following
disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following
disclaimer in the documentation and/or other materials provided with the distribution.

3. Neither the names of the RoboSense, nor Suteng Innovation Technology, nor the names of other contributors may be used
to endorse or promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*********************************************************************************************************************/

#pragma once
#include <stdio.h>
#include <cstring>  
//
// define ntohs()
// 
#ifdef _WIN32
#include <ws2tcpip.h>
#else //__linux__
#include <arpa/inet.h>
#endif

//
// define M_PI
// 
#ifndef _USE_MATH_DEFINES
#define _USE_MATH_DEFINES // for VC++, required to use const M_IP in <math.h>
#endif

#include <math.h>

#define DEGREE_TO_RADIAN(deg)  ((deg) * M_PI / 180)
#define RADIAN_TO_DEGREE(deg)  ((deg) * 180 / M_PI)

namespace robosense
{
namespace lidar
{
inline int16_t RS_SWAP_INT16(int16_t value)
{
  uint8_t* v = (uint8_t*)&value;

  uint8_t temp;
  temp = v[0];
  v[0] = v[1];
  v[1] = temp;

  return value;
}

inline int32_t u8ArrayToInt32(const uint8_t* data, uint8_t len) {
    if(len != 4)
    {
      printf("u8ArrayToInt32: len is not 4\n");
      return 0;
    }
    uint32_t uintValue = ntohl(*reinterpret_cast<const uint32_t*>(data));
    return static_cast<int32_t>(uintValue);
}

inline float convertUint32ToFloat(uint32_t byteArray) {
    float floatValue;
    std::memcpy(&floatValue, &byteArray, sizeof(float));
    return floatValue;
}

}
}


