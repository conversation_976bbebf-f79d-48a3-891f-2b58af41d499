// Generated by gencpp from file rscamera_msg/RsCompressedImage.msg
// DO NOT EDIT!


#ifndef RSCAMERA_MESSAGE_RSCOMPRESSEDIMAGE_H
#define RSCAMERA_MESSAGE_RSCOMPRESSEDIMAGE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>

namespace rscamera_msg
{
template <class ContainerAllocator>
struct RsCompressedImage_
{
  typedef RsCompressedImage_<ContainerAllocator> Type;

  RsCompressedImage_()
    : header()
    , type(0)
    , data()  {
    }
  RsCompressedImage_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , type(0)
    , data(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef uint8_t _type_type;
  _type_type type;

   typedef std::vector<uint8_t, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<uint8_t>> _data_type;
  _data_type data;





  typedef boost::shared_ptr< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> const> ConstPtr;

}; // struct RsCompressedImage_

typedef ::rscamera_msg::RsCompressedImage_<std::allocator<void> > RsCompressedImage;

typedef boost::shared_ptr< ::rscamera_msg::RsCompressedImage > RsCompressedImagePtr;
typedef boost::shared_ptr< ::rscamera_msg::RsCompressedImage const> RsCompressedImageConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rscamera_msg::RsCompressedImage_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rscamera_msg::RsCompressedImage_<ContainerAllocator1> & lhs, const ::rscamera_msg::RsCompressedImage_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.type == rhs.type &&
    lhs.data == rhs.data;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rscamera_msg::RsCompressedImage_<ContainerAllocator1> & lhs, const ::rscamera_msg::RsCompressedImage_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rscamera_msg

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> >
{
  static const char* value()
  {
    return "4044c7c7aa754eb9dc4031aaf0ca91a7";
  }

  static const char* value(const ::rscamera_msg::RsCompressedImage_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x4044c7c7aa754eb9ULL;
  static const uint64_t static_value2 = 0xdc4031aaf0ca91a7ULL;
};

template<class ContainerAllocator>
struct DataType< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rscamera_msg/RsCompressedImage";
  }

  static const char* value(const ::rscamera_msg::RsCompressedImage_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> >
{
  static const char* value()
  {
    return "std_msgs/Header header\n"
"uint8 type\n"
"uint8[] data\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
;
  }

  static const char* value(const ::rscamera_msg::RsCompressedImage_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.type);
      stream.next(m.data);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct RsCompressedImage_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rscamera_msg::RsCompressedImage_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rscamera_msg::RsCompressedImage_<ContainerAllocator>& v)
  {
    s << indent << "header: ";
    s << std::endl;
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    s << indent << "type: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.type);
    s << indent << "data[]" << std::endl;
    for (size_t i = 0; i < v.data.size(); ++i)
    {
      s << indent << "  data[" << i << "]: ";
      Printer<uint8_t>::stream(s, indent + "  ", v.data[i]);
    }
  }
};

} // namespace message_operations
} // namespace ros

#endif // RSCAMERA_MESSAGE_RSCOMPRESSEDIMAGE_H
