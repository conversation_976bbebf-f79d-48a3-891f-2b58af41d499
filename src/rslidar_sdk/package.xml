<?xml version="1.0"?>
<package format="3">
  <name>rslidar_sdk</name>
  <version>1.5.16</version>
  <description>The rslidar_sdk package</description>
  <maintainer email="<EMAIL>">robosense</maintainer>
  <license>BSD</license>

  <buildtool_depend condition="$ROS_VERSION == 1">catkin</buildtool_depend>
  <buildtool_depend condition="$ROS_VERSION == 2">ament_cmake</buildtool_depend>

  <depend>libpcap</depend>
  <depend condition="$ROS_VERSION == 1">pcl_ros</depend>
  <depend condition="$ROS_VERSION == 2">rclcpp</depend>
  <depend condition="$ROS_VERSION == 1">roscpp</depend>
  <depend condition="$ROS_VERSION == 1">roslib</depend>
  <depend condition="$ROS_VERSION == 2">rslidar_msg</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>yaml-cpp</depend>

  <export>
     <build_type condition="$ROS_VERSION == 2">ament_cmake</build_type>
  </export>
</package>
