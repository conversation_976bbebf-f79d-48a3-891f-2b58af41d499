{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-b48bf508c4af47da2f8d.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "scout_description", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "scout_description_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-scout_description_uninstall-47273a16b4ed56c158ab.json", "name": "scout_description_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-e9d4d67cfbc4237d4a12.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ros_ws/build/scout_description", "source": "/home/<USER>/ros_ws/src/scout_ros2/scout_description"}, "version": {"major": 2, "minor": 3}}