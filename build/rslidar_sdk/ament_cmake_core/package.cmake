set(_AMENT_PACKAGE_NAME "rslidar_sdk")
set(rslidar_sdk_VERSION "1.5.16")
set(rslidar_sdk_MAINTAINER "robosense <<EMAIL>>")
set(rslidar_sdk_BUILD_DEPENDS "libpcap" "rclcpp" "rslidar_msg" "sensor_msgs" "std_msgs" "yaml-cpp")
set(rslidar_sdk_BUILDTOOL_DEPENDS "ament_cmake")
set(rslidar_sdk_BUILD_EXPORT_DEPENDS "libpcap" "rclcpp" "rslidar_msg" "sensor_msgs" "std_msgs" "yaml-cpp")
set(rslidar_sdk_BUILDTOOL_EXPORT_DEPENDS )
set(rslidar_sdk_EXEC_DEPENDS "libpcap" "rclcpp" "rslidar_msg" "sensor_msgs" "std_msgs" "yaml-cpp")
set(rslidar_sdk_TEST_DEPENDS )
set(rslidar_sdk_GROUP_DEPENDS )
set(rslidar_sdk_MEMBER_OF_GROUPS )
set(rslidar_sdk_DEPRECATED "")
set(rslidar_sdk_EXPORT_TAGS)
list(APPEND rslidar_sdk_EXPORT_TAGS "<build_type condition=\"$ROS_VERSION == 2\">ament_cmake</build_type>")
