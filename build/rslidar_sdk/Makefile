# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ros_ws/src/rslidar_sdk

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ros_ws/build/rslidar_sdk

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ros_ws/build/rslidar_sdk/CMakeFiles /home/<USER>/ros_ws/build/rslidar_sdk//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ros_ws/build/rslidar_sdk/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named rslidar_sdk_uninstall

# Build rule for target.
rslidar_sdk_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 rslidar_sdk_uninstall
.PHONY : rslidar_sdk_uninstall

# fast build rule for target.
rslidar_sdk_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rslidar_sdk_uninstall.dir/build.make CMakeFiles/rslidar_sdk_uninstall.dir/build
.PHONY : rslidar_sdk_uninstall/fast

#=============================================================================
# Target rules for targets named rslidar_sdk_node

# Build rule for target.
rslidar_sdk_node: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 rslidar_sdk_node
.PHONY : rslidar_sdk_node

# fast build rule for target.
rslidar_sdk_node/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rslidar_sdk_node.dir/build.make CMakeFiles/rslidar_sdk_node.dir/build
.PHONY : rslidar_sdk_node/fast

node/rslidar_sdk_node.o: node/rslidar_sdk_node.cpp.o
.PHONY : node/rslidar_sdk_node.o

# target to build an object file
node/rslidar_sdk_node.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rslidar_sdk_node.dir/build.make CMakeFiles/rslidar_sdk_node.dir/node/rslidar_sdk_node.cpp.o
.PHONY : node/rslidar_sdk_node.cpp.o

node/rslidar_sdk_node.i: node/rslidar_sdk_node.cpp.i
.PHONY : node/rslidar_sdk_node.i

# target to preprocess a source file
node/rslidar_sdk_node.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rslidar_sdk_node.dir/build.make CMakeFiles/rslidar_sdk_node.dir/node/rslidar_sdk_node.cpp.i
.PHONY : node/rslidar_sdk_node.cpp.i

node/rslidar_sdk_node.s: node/rslidar_sdk_node.cpp.s
.PHONY : node/rslidar_sdk_node.s

# target to generate assembly for a file
node/rslidar_sdk_node.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rslidar_sdk_node.dir/build.make CMakeFiles/rslidar_sdk_node.dir/node/rslidar_sdk_node.cpp.s
.PHONY : node/rslidar_sdk_node.cpp.s

src/manager/node_manager.o: src/manager/node_manager.cpp.o
.PHONY : src/manager/node_manager.o

# target to build an object file
src/manager/node_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rslidar_sdk_node.dir/build.make CMakeFiles/rslidar_sdk_node.dir/src/manager/node_manager.cpp.o
.PHONY : src/manager/node_manager.cpp.o

src/manager/node_manager.i: src/manager/node_manager.cpp.i
.PHONY : src/manager/node_manager.i

# target to preprocess a source file
src/manager/node_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rslidar_sdk_node.dir/build.make CMakeFiles/rslidar_sdk_node.dir/src/manager/node_manager.cpp.i
.PHONY : src/manager/node_manager.cpp.i

src/manager/node_manager.s: src/manager/node_manager.cpp.s
.PHONY : src/manager/node_manager.s

# target to generate assembly for a file
src/manager/node_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rslidar_sdk_node.dir/build.make CMakeFiles/rslidar_sdk_node.dir/src/manager/node_manager.cpp.s
.PHONY : src/manager/node_manager.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... rslidar_sdk_uninstall"
	@echo "... uninstall"
	@echo "... rslidar_sdk_node"
	@echo "... node/rslidar_sdk_node.o"
	@echo "... node/rslidar_sdk_node.i"
	@echo "... node/rslidar_sdk_node.s"
	@echo "... src/manager/node_manager.o"
	@echo "... src/manager/node_manager.i"
	@echo "... src/manager/node_manager.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

