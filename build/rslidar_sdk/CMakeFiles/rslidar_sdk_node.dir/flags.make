# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp -DENABLE_DIFOP_PARSE -DENABLE_MODIFY_RECVBUF -DPOINT_TYPE_XYZI -DPROJECT_PATH=\"/home/<USER>/ros_ws/src/rslidar_sdk\" -DRCUTILS_ENABLE_FAULT_INJECTION -DROS2_FOUND

CXX_INCLUDES = -I/home/<USER>/ros_ws/src/rslidar_sdk/src -I/home/<USER>/ros_ws/src/rslidar_sdk/src/rs_driver/src -I/usr/local/rslidar_sdk/include -isystem /opt/ros/humble/include/rclcpp -isystem /opt/ros/humble/include/ament_index_cpp -isystem /opt/ros/humble/include/libstatistics_collector -isystem /opt/ros/humble/include/rcl -isystem /opt/ros/humble/include/rcpputils -isystem /opt/ros/humble/include/rcutils -isystem /opt/ros/humble/include/builtin_interfaces -isystem /opt/ros/humble/include/rosgraph_msgs -isystem /opt/ros/humble/include/rosidl_typesupport_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_c -isystem /opt/ros/humble/include/rosidl_runtime_cpp -isystem /opt/ros/humble/include/rcl_yaml_param_parser -isystem /opt/ros/humble/include/statistics_msgs -isystem /opt/ros/humble/include/tracetools -isystem /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg -isystem /opt/ros/humble/include/std_msgs -isystem /opt/ros/humble/include/sensor_msgs -isystem /opt/ros/humble/include/rosidl_runtime_c -isystem /opt/ros/humble/include/rosidl_typesupport_interface -isystem /opt/ros/humble/include/fastcdr -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/humble/include/rmw -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_cpp -isystem /opt/ros/humble/include/rcl_interfaces -isystem /opt/ros/humble/include/rcl_logging_interface -isystem /opt/ros/humble/include/libyaml_vendor -isystem /opt/ros/humble/include/geometry_msgs

CXX_FLAGS = -O3 -DNDEBUG   -O3 -std=c++17 -Wall

