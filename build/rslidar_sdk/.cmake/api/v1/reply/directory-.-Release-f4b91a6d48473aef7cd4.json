{"backtraceGraph": {"commands": ["_install", "install", "include", "find_package"], "files": ["/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_symlink_install-extras.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/ament_cmake_coreConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_include_directories-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "CMakeLists.txt"], "nodes": [{"file": 5}, {"command": 3, "file": 5, "line": 130, "parent": 0}, {"file": 4, "parent": 1}, {"command": 2, "file": 4, "line": 41, "parent": 2}, {"file": 3, "parent": 3}, {"command": 3, "file": 3, "line": 8, "parent": 4}, {"file": 2, "parent": 5}, {"command": 2, "file": 2, "line": 41, "parent": 6}, {"file": 1, "parent": 7}, {"command": 1, "file": 1, "line": 47, "parent": 8}, {"command": 0, "file": 0, "line": 43, "parent": 9}]}, "installers": [{"backtrace": 10, "component": "Unspecified", "scriptFile": "/home/<USER>/ros_ws/build/rslidar_sdk/ament_cmake_symlink_install/ament_cmake_symlink_install.cmake", "type": "script"}], "paths": {"build": ".", "source": "."}}