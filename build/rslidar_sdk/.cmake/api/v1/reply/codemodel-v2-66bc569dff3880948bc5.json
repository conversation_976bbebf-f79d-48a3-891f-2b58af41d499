{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Release-f4b91a6d48473aef7cd4.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "src/rs_driver", "jsonFile": "directory-src.rs_driver-Release-ce3bee6e0020f89a675d.json", "minimumCMakeVersion": {"string": "3.5"}, "parentIndex": 0, "projectIndex": 1, "source": "src/rs_driver"}], "name": "Release", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "rslidar_sdk", "targetIndexes": [0, 1, 2]}, {"directoryIndexes": [1], "name": "rs_driver", "parentIndex": 0}], "targets": [{"directoryIndex": 0, "id": "rslidar_sdk_node::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_sdk_node-Release-cb8d431436bc735ce3c2.json", "name": "rslidar_sdk_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_sdk_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_sdk_uninstall-Release-0f9a198586324528237e.json", "name": "rslidar_sdk_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-Release-1d113730ef48df695618.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ros_ws/build/rslidar_sdk", "source": "/home/<USER>/ros_ws/src/rslidar_sdk"}, "version": {"major": 2, "minor": 3}}