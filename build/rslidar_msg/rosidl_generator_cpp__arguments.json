{"package_name": "rslidar_msg", "output_dir": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_cpp/rslidar_msg", "template_dir": "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource", "idl_tuples": ["/home/<USER>/ros_ws/build/rslidar_msg/rosidl_adapter/rslidar_msg:msg/RslidarPacket.idl"], "ros_interface_dependencies": ["builtin_interfaces:/opt/ros/humble/share/builtin_interfaces/msg/Duration.idl", "builtin_interfaces:/opt/ros/humble/share/builtin_interfaces/msg/Time.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Bool.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Byte.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/ByteMultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Char.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/ColorRGBA.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Empty.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Float32.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Float32MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Float64.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Float64MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Header.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int16.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int16MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int32.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int32MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int64.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int64MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int8.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/Int8MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/MultiArrayDimension.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/MultiArrayLayout.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/String.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt16.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt16MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt32.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt32MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt64.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt64MultiArray.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt8.idl", "std_msgs:/opt/ros/humble/share/std_msgs/msg/UInt8MultiArray.idl"], "target_dependencies": ["/opt/ros/humble/share/rosidl_generator_cpp/cmake/../../../lib/rosidl_generator_cpp/rosidl_generator_cpp", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../../../local/lib/python3.10/dist-packages/rosidl_generator_cpp/__init__.py", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/action__builder.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/action__struct.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/action__traits.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/idl.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/idl__builder.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/idl__struct.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/idl__traits.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/msg__builder.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/msg__struct.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/msg__traits.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/srv__builder.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/srv__struct.hpp.em", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/../resource/srv__traits.hpp.em", "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_adapter/rslidar_msg/msg/RslidarPacket.idl", "/opt/ros/humble/share/builtin_interfaces/msg/Duration.idl", "/opt/ros/humble/share/builtin_interfaces/msg/Time.idl", "/opt/ros/humble/share/std_msgs/msg/Bool.idl", "/opt/ros/humble/share/std_msgs/msg/Byte.idl", "/opt/ros/humble/share/std_msgs/msg/ByteMultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Char.idl", "/opt/ros/humble/share/std_msgs/msg/ColorRGBA.idl", "/opt/ros/humble/share/std_msgs/msg/Empty.idl", "/opt/ros/humble/share/std_msgs/msg/Float32.idl", "/opt/ros/humble/share/std_msgs/msg/Float32MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Float64.idl", "/opt/ros/humble/share/std_msgs/msg/Float64MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Header.idl", "/opt/ros/humble/share/std_msgs/msg/Int16.idl", "/opt/ros/humble/share/std_msgs/msg/Int16MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Int32.idl", "/opt/ros/humble/share/std_msgs/msg/Int32MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Int64.idl", "/opt/ros/humble/share/std_msgs/msg/Int64MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/Int8.idl", "/opt/ros/humble/share/std_msgs/msg/Int8MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/MultiArrayDimension.idl", "/opt/ros/humble/share/std_msgs/msg/MultiArrayLayout.idl", "/opt/ros/humble/share/std_msgs/msg/String.idl", "/opt/ros/humble/share/std_msgs/msg/UInt16.idl", "/opt/ros/humble/share/std_msgs/msg/UInt16MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/UInt32.idl", "/opt/ros/humble/share/std_msgs/msg/UInt32MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/UInt64.idl", "/opt/ros/humble/share/std_msgs/msg/UInt64MultiArray.idl", "/opt/ros/humble/share/std_msgs/msg/UInt8.idl", "/opt/ros/humble/share/std_msgs/msg/UInt8MultiArray.idl"]}