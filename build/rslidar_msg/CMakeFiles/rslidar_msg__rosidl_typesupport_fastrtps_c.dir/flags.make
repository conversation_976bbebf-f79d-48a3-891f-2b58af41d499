# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DRCUTILS_ENABLE_FAULT_INJECTION -DROSIDL_TYPESUPPORT_FASTRTPS_C_BUILDING_DLL_rslidar_msg -DROS_PACKAGE_NAME=\"rslidar_msg\"

CXX_INCLUDES = -I/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_c -I/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c -isystem /opt/ros/humble/include/fastcdr -isystem /opt/ros/humble/include/rosidl_runtime_c -isystem /opt/ros/humble/include/rcutils -isystem /opt/ros/humble/include/rosidl_typesupport_interface -isystem /opt/ros/humble/include/rosidl_runtime_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/humble/include/rmw -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/humble/include/builtin_interfaces -isystem /opt/ros/humble/include/std_msgs

CXX_FLAGS = -fPIC -Wall -Wextra -Wpedantic -std=gnu++14

