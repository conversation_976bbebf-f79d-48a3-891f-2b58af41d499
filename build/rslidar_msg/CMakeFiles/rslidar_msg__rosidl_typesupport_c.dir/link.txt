/usr/bin/c++ -fPIC -shared -Wl,-soname,librslidar_msg__rosidl_typesupport_c.so -o librslidar_msg__rosidl_typesupport_c.so CMakeFiles/rslidar_msg__rosidl_typesupport_c.dir/rosidl_typesupport_c/rslidar_msg/msg/rslidar_packet__type_support.cpp.o  -Wl,-rpath,/home/<USER>/ros_ws/build/rslidar_msg:/opt/ros/humble/lib: librslidar_msg__rosidl_generator_c.so /opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so /opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so /opt/ros/humble/lib/librosidl_typesupport_c.so /opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so /opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so /opt/ros/humble/lib/librosidl_runtime_c.so /opt/ros/humble/lib/librcutils.so -ldl -Wl,-rpath-link,/opt/ros/humble/lib 
