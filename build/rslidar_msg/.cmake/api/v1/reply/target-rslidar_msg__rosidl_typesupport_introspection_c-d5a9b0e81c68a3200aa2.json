{"artifacts": [{"path": "librs<PERSON><PERSON>_msg__rosidl_typesupport_introspection_c.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "_install", "ament_cmake_symlink_install_targets", "install", "target_link_libraries", "set_target_properties", "add_definitions", "find_package", "target_include_directories"], "files": ["/opt/ros/humble/share/rosidl_typesupport_introspection_c/cmake/rosidl_typesupport_introspection_c_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/ament_cmake_symlink_install_targets.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 100, "parent": 4}, {"command": 6, "file": 0, "line": 146, "parent": 4}, {"command": 5, "file": 5, "line": 37, "parent": 6}, {"command": 4, "file": 4, "line": 50, "parent": 7}, {"command": 7, "file": 0, "line": 122, "parent": 4}, {"command": 7, "file": 0, "line": 129, "parent": 4}, {"command": 7, "file": 0, "line": 129, "parent": 4}, {"command": 7, "file": 0, "line": 125, "parent": 4}, {"command": 8, "file": 0, "line": 109, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 8, "parent": 14}, {"command": 10, "file": 8, "line": 21, "parent": 15}, {"file": 7, "parent": 16}, {"command": 1, "file": 7, "line": 41, "parent": 17}, {"file": 6, "parent": 18}, {"command": 9, "file": 6, "line": 25, "parent": 19}, {"command": 11, "file": 0, "line": 116, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 13, "fragment": "-Wall"}, {"fragment": "-std=gnu11"}], "defines": [{"backtrace": 9, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_TYPESUPPORT_INTROSPECTION_C_BUILDING_DLL_rslidar_msg"}, {"backtrace": 20, "define": "ROS_PACKAGE_NAME=\"rslidar_msg\""}], "includes": [{"backtrace": 21, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_introspection_c"}, {"backtrace": 9, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}], "language": "C", "languageStandard": {"backtraces": [13], "standard": "11"}, "sourceIndexes": [1]}], "dependencies": [{"backtrace": 9, "id": "rslidar_msg__rosidl_generator_c::@6890427a1f51a3e7e1df"}], "id": "rslidar_msg__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 8, "path": "lib"}, {"backtrace": 8, "path": "lib"}], "prefix": {"path": "/home/<USER>/ros_ws/install/rslidar_msg"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ros_ws/build/rslidar_msg:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 9, "fragment": "librslidar_msg__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "C"}, "name": "rslidar_msg__rosidl_typesupport_introspection_c", "nameOnDisk": "librs<PERSON><PERSON>_msg__rosidl_typesupport_introspection_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1]}, {"name": "CMake Rules", "sourceIndexes": [2]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_introspection_c/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_introspection_c/rslidar_msg/msg/detail/rslidar_packet__type_support.c", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_introspection_c/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_c.h.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}