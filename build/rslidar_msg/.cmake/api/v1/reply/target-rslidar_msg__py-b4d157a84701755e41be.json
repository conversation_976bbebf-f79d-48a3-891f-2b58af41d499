{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["/home/<USER>/ros_ws/build/rslidar_msg/rslidar_msg__py/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}]}, "dependencies": [{"id": "rslidar_msg::@6890427a1f51a3e7e1df"}], "id": "rslidar_msg__py::@c80b5731effdf274c015", "name": "rslidar_msg__py", "paths": {"build": "rslidar_msg__py", "source": "/home/<USER>/ros_ws/build/rslidar_msg/rslidar_msg__py"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rslidar_msg__py/CMakeFiles/rslidar_msg__py", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rslidar_msg__py/CMakeFiles/rslidar_msg__py.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_py/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}