{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-3291eece070a6ec3d9ff.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, {"build": "rslidar_msg__py", "jsonFile": "directory-rslidar_msg__py-b6b9ba8c396c1639784e.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 0, "projectIndex": 0, "source": "/home/<USER>/ros_ws/build/rslidar_msg/rslidar_msg__py", "targetIndexes": [4]}], "name": "", "projects": [{"directoryIndexes": [0, 1], "name": "rslidar_msg", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}], "targets": [{"directoryIndex": 0, "id": "ament_cmake_python_build_rslidar_msg_egg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_build_rslidar_msg_egg-51c62369a1eea4204c7a.json", "name": "ament_cmake_python_build_rslidar_msg_egg", "projectIndex": 0}, {"directoryIndex": 0, "id": "ament_cmake_python_symlink_rslidar_msg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_symlink_rslidar_msg-36bbd1120394b77e6e1f.json", "name": "ament_cmake_python_symlink_rslidar_msg", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg-0ab45eb43e5b61bd03d0.json", "name": "rslidar_msg", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__cpp-7e62bb369cf84dd4be03.json", "name": "rslidar_msg__cpp", "projectIndex": 0}, {"directoryIndex": 1, "id": "rslidar_msg__py::@c80b5731effdf274c015", "jsonFile": "target-rslidar_msg__py-b4d157a84701755e41be.json", "name": "rslidar_msg__py", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_generator_c::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_generator_c-79ef67b80ac910738614.json", "name": "rslidar_msg__rosidl_generator_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_generator_py::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_generator_py-145bb77a2c85a2a6fe02.json", "name": "rslidar_msg__rosidl_generator_py", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_c-a0a24008d2fdd458eac7.json", "name": "rslidar_msg__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_c__pyext::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_c__pyext-857dfcd6f582f449d143.json", "name": "rslidar_msg__rosidl_typesupport_c__pyext", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_cpp-bf8f868f74d334a7c48b.json", "name": "rslidar_msg__rosidl_typesupport_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_fastrtps_c-0a3af73e7b0d9e4cee54.json", "name": "rslidar_msg__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_fastrtps_c__pyext::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_fastrtps_c__pyext-cbd758dc3df17990ae2a.json", "name": "rslidar_msg__rosidl_typesupport_fastrtps_c__pyext", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_fastrtps_cpp-2a8ee380ef1b9bf424fb.json", "name": "rslidar_msg__rosidl_typesupport_fastrtps_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_introspection_c-d5a9b0e81c68a3200aa2.json", "name": "rslidar_msg__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_introspection_c__pyext::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_introspection_c__pyext-99219ac53917a55c48cc.json", "name": "rslidar_msg__rosidl_typesupport_introspection_c__pyext", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg__rosidl_typesupport_introspection_cpp-0e17ca574792690f5367.json", "name": "rslidar_msg__rosidl_typesupport_introspection_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "rslidar_msg_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-rslidar_msg_uninstall-5d474d8b27d082eaf5cc.json", "name": "rslidar_msg_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-008531f139dac3a9d628.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ros_ws/build/rslidar_msg", "source": "/home/<USER>/ros_ws/src/rslidar_msg"}, "version": {"major": 2, "minor": 3}}