{"artifacts": [{"path": "librslidar_msg__rosidl_typesupport_fastrtps_c.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "_install", "ament_cmake_symlink_install_targets", "install", "target_link_libraries", "add_compile_options", "add_definitions", "find_package", "target_include_directories", "set_target_properties"], "files": ["/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/ament_cmake_symlink_install_targets.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 104, "parent": 4}, {"command": 6, "file": 0, "line": 167, "parent": 4}, {"command": 5, "file": 5, "line": 37, "parent": 6}, {"command": 4, "file": 4, "line": 50, "parent": 7}, {"command": 7, "file": 0, "line": 131, "parent": 4}, {"command": 7, "file": 0, "line": 141, "parent": 4}, {"command": 7, "file": 0, "line": 141, "parent": 4}, {"command": 7, "file": 0, "line": 115, "parent": 4}, {"command": 8, "file": 3, "line": 15, "parent": 0}, {"command": 10, "file": 0, "line": 21, "parent": 4}, {"file": 7, "parent": 14}, {"command": 1, "file": 7, "line": 41, "parent": 15}, {"file": 6, "parent": 16}, {"command": 9, "file": 6, "line": 25, "parent": 17}, {"command": 11, "file": 0, "line": 134, "parent": 4}, {"command": 12, "file": 0, "line": 110, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 13, "fragment": "-Wall"}, {"backtrace": 13, "fragment": "-Wextra"}, {"backtrace": 13, "fragment": "-Wpedantic"}, {"fragment": "-std=gnu++14"}], "defines": [{"backtrace": 12, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_TYPESUPPORT_FASTRTPS_C_BUILDING_DLL_rslidar_msg"}, {"backtrace": 18, "define": "ROS_PACKAGE_NAME=\"rslidar_msg\""}], "includes": [{"backtrace": 19, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_c"}, {"backtrace": 9, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [20], "standard": "14"}, "sourceIndexes": [1]}], "dependencies": [{"backtrace": 9, "id": "rslidar_msg__rosidl_generator_c::@6890427a1f51a3e7e1df"}], "id": "rslidar_msg__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 8, "path": "lib"}, {"backtrace": 8, "path": "lib"}], "prefix": {"path": "/home/<USER>/ros_ws/install/rslidar_msg"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ros_ws/build/rslidar_msg:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 9, "fragment": "librslidar_msg__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "rslidar_msg__rosidl_typesupport_fastrtps_c", "nameOnDisk": "librslidar_msg__rosidl_typesupport_fastrtps_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1]}, {"name": "CMake Rules", "sourceIndexes": [2]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_c/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_c/rslidar_msg/msg/detail/rslidar_packet__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_fastrtps_c/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_c.h.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}