{"artifacts": [{"path": "librs<PERSON>ar_msg__rosidl_typesupport_introspection_cpp.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "_install", "ament_cmake_symlink_install_targets", "install", "target_link_libraries", "add_dependencies", "add_compile_options", "add_definitions", "find_package", "target_include_directories", "set_property"], "files": ["/opt/ros/humble/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/ament_cmake_symlink_install_targets.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 93, "parent": 4}, {"command": 6, "file": 0, "line": 146, "parent": 4}, {"command": 5, "file": 5, "line": 37, "parent": 6}, {"command": 4, "file": 4, "line": 50, "parent": 7}, {"command": 7, "file": 0, "line": 128, "parent": 4}, {"command": 7, "file": 0, "line": 128, "parent": 4}, {"command": 7, "file": 0, "line": 121, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 6, "parent": 12}, {"command": 8, "file": 6, "line": 139, "parent": 13}, {"command": 9, "file": 3, "line": 15, "parent": 0}, {"command": 7, "file": 0, "line": 117, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 9, "parent": 17}, {"command": 11, "file": 9, "line": 21, "parent": 18}, {"file": 8, "parent": 19}, {"command": 1, "file": 8, "line": 41, "parent": 20}, {"file": 7, "parent": 21}, {"command": 10, "file": 7, "line": 25, "parent": 22}, {"command": 12, "file": 0, "line": 111, "parent": 4}, {"command": 13, "file": 0, "line": 103, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 15, "fragment": "-Wall"}, {"backtrace": 15, "fragment": "-Wextra"}, {"backtrace": 15, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 16, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_BUILDING_DLL"}, {"backtrace": 23, "define": "ROS_PACKAGE_NAME=\"rslidar_msg\""}], "includes": [{"backtrace": 24, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_introspection_cpp"}, {"backtrace": 16, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_cpp"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}], "language": "CXX", "languageStandard": {"backtraces": [25], "standard": "17"}, "sourceIndexes": [1]}], "dependencies": [{"backtrace": 14, "id": "rslidar_msg__cpp::@6890427a1f51a3e7e1df"}], "id": "rslidar_msg__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 8, "path": "lib"}, {"backtrace": 8, "path": "lib"}], "prefix": {"path": "/home/<USER>/ros_ws/install/rslidar_msg"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "rslidar_msg__rosidl_typesupport_introspection_cpp", "nameOnDisk": "librs<PERSON>ar_msg__rosidl_typesupport_introspection_cpp.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1]}, {"name": "CMake Rules", "sourceIndexes": [2]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_introspection_cpp/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_introspection_cpp/rslidar_msg/msg/detail/rslidar_packet__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_typesupport_introspection_cpp/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_cpp.hpp.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}