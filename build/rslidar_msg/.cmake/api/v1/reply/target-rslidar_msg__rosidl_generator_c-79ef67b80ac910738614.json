{"artifacts": [{"path": "librslidar_msg__rosidl_generator_c.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "_install", "ament_cmake_symlink_install_targets", "install", "target_link_libraries", "set_target_properties", "add_definitions", "find_package", "target_include_directories"], "files": ["/opt/ros/humble/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/ament_cmake_symlink_install_targets.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/symlink_install/install.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 110, "parent": 4}, {"command": 6, "file": 0, "line": 164, "parent": 4}, {"command": 5, "file": 5, "line": 37, "parent": 6}, {"command": 4, "file": 4, "line": 50, "parent": 7}, {"command": 7, "file": 0, "line": 132, "parent": 4}, {"command": 7, "file": 0, "line": 132, "parent": 4}, {"command": 7, "file": 0, "line": 137, "parent": 4}, {"command": 8, "file": 0, "line": 119, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 8, "parent": 13}, {"command": 10, "file": 8, "line": 21, "parent": 14}, {"file": 7, "parent": 15}, {"command": 1, "file": 7, "line": 41, "parent": 16}, {"file": 6, "parent": 17}, {"command": 9, "file": 6, "line": 25, "parent": 18}, {"command": 11, "file": 0, "line": 125, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 12, "fragment": "-Wall"}, {"fragment": "-std=gnu11"}], "defines": [{"backtrace": 10, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_GENERATOR_C_BUILDING_DLL_rslidar_msg"}, {"backtrace": 19, "define": "ROS_PACKAGE_NAME=\"rslidar_msg\""}], "includes": [{"backtrace": 20, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 10, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}], "language": "C", "languageStandard": {"backtraces": [12], "standard": "11"}, "sourceIndexes": [4]}], "id": "rslidar_msg__rosidl_generator_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 8, "path": "lib"}, {"backtrace": 8, "path": "lib"}], "prefix": {"path": "/home/<USER>/ros_ws/install/rslidar_msg"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "C"}, "name": "rslidar_msg__rosidl_generator_c", "nameOnDisk": "librslidar_msg__rosidl_generator_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3]}, {"name": "Source Files", "sourceIndexes": [4]}, {"name": "CMake Rules", "sourceIndexes": [5]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c/rslidar_msg/msg/rslidar_packet.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c/rslidar_msg/msg/detail/rslidar_packet__functions.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c/rslidar_msg/msg/detail/rslidar_packet__struct.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c/rslidar_msg/msg/detail/rslidar_packet__type_support.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c/rslidar_msg/msg/detail/rslidar_packet__functions.c", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ros_ws/build/rslidar_msg/rosidl_generator_c/rslidar_msg/msg/rslidar_packet.h.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}