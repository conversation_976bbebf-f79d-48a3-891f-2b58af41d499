set(_AMENT_PACKAGE_NAME "rslidar_msg")
set(rslidar_msg_VERSION "0.0.0")
set(rslidar_msg_MAINTAINER "robosense <<EMAIL>>")
set(rslidar_msg_BUILD_DEPENDS "builtin_interfaces" "rosidl_default_generators" "std_msgs" "rclcpp")
set(rslidar_msg_BUILDTOOL_DEPENDS "ament_cmake")
set(rslidar_msg_BUILD_EXPORT_DEPENDS "std_msgs" "rclcpp")
set(rslidar_msg_BUILDTOOL_EXPORT_DEPENDS )
set(rslidar_msg_EXEC_DEPENDS "builtin_interfaces" "rosidl_default_runtime" "std_msgs" "rclcpp")
set(rslidar_msg_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(rslidar_msg_GROUP_DEPENDS )
set(rslidar_msg_MEMBER_OF_GROUPS "rosidl_interface_packages")
set(rslidar_msg_DEPRECATED "")
set(rslidar_msg_EXPORT_TAGS)
list(APPEND rslidar_msg_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
