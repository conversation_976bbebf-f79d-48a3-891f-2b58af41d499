#!/usr/bin/env python3
"""
任务控制节点
用于发送目标点控制无人车/船按照预定轨迹行驶，并获取GCS发送的航线信息
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy

import math
from typing import List, Optional

# MAVROS消息类型
from mavros_msgs.msg import (
    WaypointList, Waypoint, State, GlobalPositionTarget,
    PositionTarget, CommandCode
)
from mavros_msgs.srv import (
    WaypointPull, WaypointPush, WaypointClear, 
    CommandBool, CommandTOL, SetMode
)

# 标准ROS消息类型
from geometry_msgs.msg import PoseStamped, Point, Quaternion
from geographic_msgs.msg import GeoPoint
from std_msgs.msg import Header
from nav_msgs.msg import Path, OccupancyGrid
from sensor_msgs.msg import NavSatFix
from geometry_msgs.msg import PoseWithCovarianceStamped

# Navigation2相关导入
try:
    from nav2_simple_commander.robot_navigator import BasicNavigator
    from geometry_msgs.msg import PoseStamped as Nav2PoseStamped
    NAV2_AVAILABLE = True
except ImportError:
    NAV2_AVAILABLE = False


class MissionControlNode(Node):
    """任务控制节点"""
    
    def __init__(self):
        super().__init__('mission_control_node')
        
        # 声明参数
        self.declare_parameters(
            namespace='',
            parameters=[
                # 控制参数
                ('auto_mission_start', False),    # 自动开始任务
                ('mission_timeout', 300.0),       # 任务超时时间(秒)
                ('waypoint_tolerance', 2.0),      # 航点容差(米)
                ('max_speed', 2.0),               # 最大速度(m/s)
                
                # 话题名称
                ('mavros_namespace', '/mavros'),
                ('mission_path_topic', '/mission_path'),
                ('target_pose_topic', '/target_pose'),
                
                # 坐标系
                ('global_frame_id', 'map'),
                ('local_frame_id', 'base_link'),

                # Navigation2集成
                ('enable_navigation2', True),     # 启用Navigation2集成
                ('nav2_timeout', 30.0),          # Navigation2超时时间
                ('waypoint_update_distance', 5.0), # waypoint更新距离阈值
            ]
        )
        
        # 获取参数
        self.auto_mission_start = self.get_parameter('auto_mission_start').value
        self.mission_timeout = self.get_parameter('mission_timeout').value
        self.waypoint_tolerance = self.get_parameter('waypoint_tolerance').value
        self.max_speed = self.get_parameter('max_speed').value
        
        self.mavros_ns = self.get_parameter('mavros_namespace').value
        self.mission_path_topic = self.get_parameter('mission_path_topic').value
        self.target_pose_topic = self.get_parameter('target_pose_topic').value
        
        self.global_frame_id = self.get_parameter('global_frame_id').value
        self.local_frame_id = self.get_parameter('local_frame_id').value

        # Navigation2参数
        self.enable_navigation2 = self.get_parameter('enable_navigation2').value
        self.nav2_timeout = self.get_parameter('nav2_timeout').value
        self.waypoint_update_distance = self.get_parameter('waypoint_update_distance').value
        
        # QoS配置
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        
        # 订阅者
        self.state_subscriber = self.create_subscription(
            State,
            f'{self.mavros_ns}/state',
            self.state_callback,
            qos_profile
        )
        
        self.waypoint_subscriber = self.create_subscription(
            WaypointList,
            f'{self.mavros_ns}/mission/waypoints',
            self.waypoint_callback,
            qos_profile
        )
        
        self.global_position_subscriber = self.create_subscription(
            NavSatFix,
            f'{self.mavros_ns}/global_position/global',
            self.global_position_callback,
            qos_profile
        )
        
        self.local_position_subscriber = self.create_subscription(
            PoseStamped,
            f'{self.mavros_ns}/local_position/pose',
            self.local_position_callback,
            qos_profile
        )
        
        # 发布者
        self.mission_path_publisher = self.create_publisher(
            Path,
            self.mission_path_topic,
            10
        )
        
        self.setpoint_global_publisher = self.create_publisher(
            GlobalPositionTarget,
            f'{self.mavros_ns}/setpoint_raw/global',
            10
        )
        
        self.setpoint_local_publisher = self.create_publisher(
            PoseStamped,
            f'{self.mavros_ns}/setpoint_position/local',
            10
        )
        
        self.target_pose_publisher = self.create_publisher(
            PoseStamped,
            self.target_pose_topic,
            10
        )
        
        # 服务客户端
        self.waypoint_pull_client = self.create_client(
            WaypointPull, f'{self.mavros_ns}/mission/pull'
        )
        self.waypoint_push_client = self.create_client(
            WaypointPush, f'{self.mavros_ns}/mission/push'
        )
        self.waypoint_clear_client = self.create_client(
            WaypointClear, f'{self.mavros_ns}/mission/clear'
        )
        self.arming_client = self.create_client(
            CommandBool, f'{self.mavros_ns}/cmd/arming'
        )
        self.set_mode_client = self.create_client(
            SetMode, f'{self.mavros_ns}/set_mode'
        )
        
        # 状态变量
        self.current_state = State()
        self.current_waypoints: List[Waypoint] = []
        self.current_global_position: Optional[NavSatFix] = None
        self.current_local_position: Optional[PoseStamped] = None
        self.mission_active = False
        self.current_waypoint_index = 0

        # Navigation2相关状态
        self.nav2_active = False
        self.global_path: Optional[Path] = None
        self.navigator = None

        # 初始化Navigation2 (如果可用)
        if self.enable_navigation2 and NAV2_AVAILABLE:
            try:
                self.navigator = BasicNavigator()
                self.get_logger().info('Navigation2集成已启用')
            except Exception as e:
                self.get_logger().warn(f'Navigation2初始化失败: {e}')
                self.enable_navigation2 = False
        elif not NAV2_AVAILABLE:
            self.get_logger().warn('Navigation2不可用，请安装nav2_simple_commander')
            self.enable_navigation2 = False
        
        # 定时器
        self.mission_timer = self.create_timer(1.0, self.mission_timer_callback)
        self.status_timer = self.create_timer(5.0, self.status_timer_callback)
        
        self.get_logger().info('任务控制节点已启动')
        self.get_logger().info(f'MAVROS命名空间: {self.mavros_ns}')
        
        # 初始化时拉取航线
        self.create_timer(2.0, self.initial_waypoint_pull)
    
    def state_callback(self, msg: State):
        """飞控状态回调"""
        self.current_state = msg
        
        # 检查模式变化
        if msg.mode == "AUTO" and not self.mission_active:
            self.mission_active = True
            self.get_logger().info('任务模式激活')
        elif msg.mode != "AUTO" and self.mission_active:
            self.mission_active = False
            self.get_logger().info('任务模式停用')
    
    def waypoint_callback(self, msg: WaypointList):
        """航点列表回调"""
        self.current_waypoints = msg.waypoints
        self.get_logger().info(f'收到航线，包含 {len(self.current_waypoints)} 个航点')
        
        # 发布航线路径用于可视化
        self.publish_mission_path()
    
    def global_position_callback(self, msg: NavSatFix):
        """全局位置回调"""
        self.current_global_position = msg
    
    def local_position_callback(self, msg: PoseStamped):
        """本地位置回调"""
        self.current_local_position = msg
    
    def publish_mission_path(self):
        """发布任务路径用于可视化和Navigation2全局路径"""
        if not self.current_waypoints:
            return

        # 创建全局路径消息
        global_path_msg = Path()
        global_path_msg.header.stamp = self.get_clock().now().to_msg()
        global_path_msg.header.frame_id = self.global_frame_id

        for waypoint in self.current_waypoints:
            if waypoint.command == CommandCode.NAV_WAYPOINT:
                pose = PoseStamped()
                pose.header = global_path_msg.header

                # GPS坐标转换为本地坐标 (简化示例，实际需要GPS转换)
                pose.pose.position.x = waypoint.x_lat  # 实际应转换为本地x
                pose.pose.position.y = waypoint.y_long # 实际应转换为本地y
                pose.pose.position.z = waypoint.z_alt

                # 计算航向 (指向下一个waypoint)
                pose.pose.orientation.w = 1.0

                global_path_msg.poses.append(pose)

        # 发布全局路径 (用于可视化)
        self.mission_path_publisher.publish(global_path_msg)

        # 如果在AUTO模式，启动Navigation2局部规划
        if self.current_state.mode == "AUTO" and len(global_path_msg.poses) > 0:
            self.start_navigation2_planning(global_path_msg)
    
    def send_global_setpoint(self, lat: float, lon: float, alt: float = 0.0):
        """发送全局设定点"""
        target = GlobalPositionTarget()
        target.header.stamp = self.get_clock().now().to_msg()
        target.header.frame_id = self.global_frame_id
        target.coordinate_frame = GlobalPositionTarget.FRAME_GLOBAL_REL_ALT
        target.type_mask = (
            GlobalPositionTarget.IGNORE_VX |
            GlobalPositionTarget.IGNORE_VY |
            GlobalPositionTarget.IGNORE_VZ |
            GlobalPositionTarget.IGNORE_AFX |
            GlobalPositionTarget.IGNORE_AFY |
            GlobalPositionTarget.IGNORE_AFZ |
            GlobalPositionTarget.IGNORE_YAW_RATE
        )
        
        target.latitude = lat
        target.longitude = lon
        target.altitude = alt
        
        self.setpoint_global_publisher.publish(target)
        
        # 同时发布目标位姿用于可视化
        pose_msg = PoseStamped()
        pose_msg.header = target.header
        pose_msg.pose.position.x = lat
        pose_msg.pose.position.y = lon
        pose_msg.pose.position.z = alt
        pose_msg.pose.orientation.w = 1.0
        
        self.target_pose_publisher.publish(pose_msg)
    
    def mission_timer_callback(self):
        """任务定时器回调"""
        if not self.mission_active or not self.current_waypoints:
            return
        
        # 这里可以添加任务执行逻辑
        # 例如：检查是否到达当前航点，切换到下一个航点等
        pass
    
    def status_timer_callback(self):
        """状态定时器回调"""
        self.get_logger().info(
            f'状态: 连接={self.current_state.connected}, '
            f'解锁={self.current_state.armed}, '
            f'模式={self.current_state.mode}, '
            f'航点数={len(self.current_waypoints)}'
        )
    
    def initial_waypoint_pull(self):
        """初始航点拉取"""
        if self.waypoint_pull_client.wait_for_service(timeout_sec=5.0):
            request = WaypointPull.Request()
            future = self.waypoint_pull_client.call_async(request)
            future.add_done_callback(self.waypoint_pull_callback)
        else:
            self.get_logger().warn('航点拉取服务不可用')
    
    def waypoint_pull_callback(self, future):
        """航点拉取回调"""
        try:
            response = future.result()
            if response.success:
                self.get_logger().info(f'成功拉取 {response.wp_received} 个航点')
            else:
                self.get_logger().warn('航点拉取失败')
        except Exception as e:
            self.get_logger().error(f'航点拉取异常: {str(e)}')

    def start_navigation2_planning(self, global_path: Path):
        """启动Navigation2局部路径规划"""
        if not self.enable_navigation2 or not self.navigator:
            return

        try:
            self.get_logger().info('启动Navigation2局部路径规划')
            self.global_path = global_path
            self.nav2_active = True

            # 设置初始位姿 (如果有当前位置)
            if self.current_local_position:
                initial_pose = PoseWithCovarianceStamped()
                initial_pose.header = self.current_local_position.header
                initial_pose.pose.pose = self.current_local_position.pose
                self.navigator.setInitialPose(initial_pose)

            # 开始跟随全局路径
            self.follow_global_path()

        except Exception as e:
            self.get_logger().error(f'Navigation2启动失败: {e}')
            self.nav2_active = False

    def follow_global_path(self):
        """跟随全局路径，使用Navigation2进行局部避障"""
        if not self.global_path or not self.global_path.poses:
            return

        # 获取下一个目标点 (简化：使用路径中的下一个点)
        if self.current_waypoint_index < len(self.global_path.poses):
            target_pose = self.global_path.poses[self.current_waypoint_index]

            self.get_logger().info(
                f'Navigation2导航到目标点 {self.current_waypoint_index + 1}/'
                f'{len(self.global_path.poses)}'
            )

            # 使用Navigation2导航到目标点
            self.navigator.goToPose(target_pose)

            # 启动监控定时器
            self.create_timer(1.0, self.check_navigation_progress)

    def check_navigation_progress(self):
        """检查Navigation2导航进度"""
        if not self.nav2_active or not self.navigator:
            return

        # 检查是否到达目标点
        if self.navigator.isTaskComplete():
            result = self.navigator.getResult()

            if result == 3:  # SUCCEEDED
                self.get_logger().info(f'到达目标点 {self.current_waypoint_index + 1}')
                self.current_waypoint_index += 1

                # 继续下一个目标点
                if self.current_waypoint_index < len(self.global_path.poses):
                    self.follow_global_path()
                else:
                    self.get_logger().info('全局路径执行完成')
                    self.nav2_active = False
            else:
                self.get_logger().warn(f'Navigation2导航失败，结果: {result}')
                self.handle_navigation_failure()

    def handle_navigation_failure(self):
        """处理Navigation2导航失败"""
        self.get_logger().warn('Navigation2导航失败，尝试恢复或切换到下一个目标点')

        # 简单的恢复策略：跳过当前目标点
        self.current_waypoint_index += 1
        if self.current_waypoint_index < len(self.global_path.poses):
            self.follow_global_path()
        else:
            self.nav2_active = False

    def convert_nav2_path_to_waypoints(self, nav2_path: Path) -> List[Waypoint]:
        """将Navigation2路径转换为ArduPilot waypoint格式"""
        waypoints = []

        for i, pose_stamped in enumerate(nav2_path.poses):
            waypoint = Waypoint()
            waypoint.frame = 3  # MAV_FRAME_GLOBAL_RELATIVE_ALT
            waypoint.command = CommandCode.NAV_WAYPOINT
            waypoint.is_current = (i == 0)
            waypoint.autocontinue = True

            # 坐标转换 (本地坐标转GPS坐标，这里需要实际的转换逻辑)
            waypoint.x_lat = pose_stamped.pose.position.x  # 需要转换为纬度
            waypoint.y_long = pose_stamped.pose.position.y # 需要转换为经度
            waypoint.z_alt = pose_stamped.pose.position.z

            waypoints.append(waypoint)

        return waypoints


def main(args=None):
    """主函数"""
    rclpy.init(args=args)
    
    try:
        node = MissionControlNode()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'节点运行出错: {e}')
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
