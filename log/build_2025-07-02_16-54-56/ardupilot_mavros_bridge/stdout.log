running develop
running egg_info
writing ardupilot_mavros_bridge.egg-info/PKG-INFO
writing dependency_links to ardupilot_mavros_bridge.egg-info/dependency_links.txt
writing entry points to ardupilot_mavros_bridge.egg-info/entry_points.txt
writing requirements to ardupilot_mavros_bridge.egg-info/requires.txt
writing top-level names to ardupilot_mavros_bridge.egg-info/top_level.txt
reading manifest file 'ardupilot_mavros_bridge.egg-info/SOURCES.txt'
writing manifest file 'ardupilot_mavros_bridge.egg-info/SOURCES.txt'
running build_ext
Creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot-mavros-bridge.egg-link (link to .)
Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
Installing mission_control_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge

Installed /home/<USER>/ros_ws/build/ardupilot_mavros_bridge
running symlink_data
