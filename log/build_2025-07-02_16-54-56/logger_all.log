[0.054s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--symlink-install']
[0.054s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7940d1017d60>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7940d11c5510>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7940d11c5510>>)
[0.142s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.142s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.142s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.142s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.142s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.142s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.142s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros_ws'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.149s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ignore', 'ignore_ament_install']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore_ament_install'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_pkg']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_pkg'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_meta']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_meta'
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ros']
[0.150s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ros'
[0.152s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ardupilot_mavros_bridge' with type 'ros.ament_python' and name 'ardupilot_mavros_bridge'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ignore', 'ignore_ament_install']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore_ament_install'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_pkg']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_pkg'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_meta']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_meta'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ros']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ros'
[0.152s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_msg' with type 'ros.ament_cmake' and name 'rslidar_msg'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore_ament_install'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_pkg']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_pkg'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_meta']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_meta'
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ros']
[0.152s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ros'
[0.156s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_sdk' with type 'ros.ament_cmake' and name 'rslidar_sdk'
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.174s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.174s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.174s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 3 installed packages in /home/<USER>/ros_ws/install
[0.175s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 324 installed packages in /opt/ros/humble
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.195s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_args' from command line to 'None'
[0.195s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target' from command line to 'None'
[0.195s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.195s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[0.195s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_first' from command line to 'False'
[0.195s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_force_configure' from command line to 'False'
[0.195s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'ament_cmake_args' from command line to 'None'
[0.195s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[0.195s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.195s] DEBUG:colcon.colcon_core.verb:Building package 'ardupilot_mavros_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge', 'symlink_install': True, 'test_result_base': None}
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_args' from command line to 'None'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target' from command line to 'None'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_cache' from command line to 'False'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_first' from command line to 'False'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_force_configure' from command line to 'False'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'ament_cmake_args' from command line to 'None'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_cmake_args' from command line to 'None'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.196s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_msg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_msg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_msg', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_msg', 'symlink_install': True, 'test_result_base': None}
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_args' from command line to 'None'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target' from command line to 'None'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.196s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.196s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_sdk', 'symlink_install': True, 'test_result_base': None}
[0.196s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.197s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.197s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_msg' with build type 'ament_cmake'
[0.197s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_msg'
[0.198s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.198s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.198s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.200s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' with build type 'ament_python'
[0.200s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'ament_prefix_path')
[0.200s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.ps1'
[0.200s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.dsv'
[0.200s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.sh'
[0.201s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.201s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.206s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.345s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge'
[0.345s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.345s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.405s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.414s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[0.455s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[0.455s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[0.457s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[0.457s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[0.457s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[0.457s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[0.458s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[0.458s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[0.458s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[0.458s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[0.458s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[0.459s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.459s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[0.460s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[0.460s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[0.460s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[0.461s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[0.461s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[0.461s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[0.461s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[0.462s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[0.462s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[0.462s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[0.462s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[0.462s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[0.463s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[0.463s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[0.463s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[0.463s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[0.463s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[0.463s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.463s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[0.463s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[0.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.464s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[0.464s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[0.464s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[0.464s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[0.465s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[0.465s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[0.465s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_sdk' with build type 'ament_cmake'
[0.465s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_sdk'
[0.465s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.465s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.472s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.524s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build --no-deps symlink_data
[0.528s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.530s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.544s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[0.545s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[0.545s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.545s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[0.546s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[0.546s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[0.547s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[0.547s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[0.548s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[0.548s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.548s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[0.548s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[0.548s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.549s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[0.549s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[0.549s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[0.550s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[0.550s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[0.550s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[0.551s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[0.551s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[0.552s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[0.552s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[0.552s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[0.553s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[0.553s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.554s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[0.555s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[0.555s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[0.555s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[0.555s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[0.556s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[0.735s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'pythonpath_develop')
[0.736s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build --no-deps symlink_data
[0.736s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath_develop.ps1'
[0.737s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath_develop.dsv'
[0.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath_develop.sh'
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake module files
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake config files
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/pkgconfig/ardupilot_mavros_bridge.pc'
[0.738s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages'
[0.738s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'pythonpath')
[0.738s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.ps1'
[0.738s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.dsv'
[0.738s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.sh'
[0.739s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.739s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ardupilot_mavros_bridge)
[0.739s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.ps1'
[0.739s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.dsv'
[0.739s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.sh'
[0.739s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.bash'
[0.740s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.zsh'
[0.740s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/colcon-core/packages/ardupilot_mavros_bridge)
[0.740s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.740s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.740s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.740s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.745s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.745s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.745s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.754s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.754s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.ps1'
[0.754s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_ps1.py'
[0.756s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.ps1'
[0.757s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.sh'
[0.758s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_sh.py'
[0.759s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.sh'
[0.760s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.bash'
[0.761s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.bash'
[0.763s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.zsh'
[0.763s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.zsh'
