[0.290s] Invoking command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
[0.406s] running egg_info
[0.407s] writing ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/PKG-INFO
[0.407s] writing dependency_links to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/dependency_links.txt
[0.407s] writing entry points to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/entry_points.txt
[0.407s] writing requirements to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/requires.txt
[0.407s] writing top-level names to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/top_level.txt
[0.408s] reading manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.408s] writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.408s] running build
[0.408s] running build_py
[0.409s] running install
[0.409s] running install_lib
[0.409s] running install_data
[0.409s] copying launch/apm.launch -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.409s] running install_egg_info
[0.410s] removing '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info' (and everything under it)
[0.410s] Copying ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info
[0.411s] running install_scripts
[0.422s] Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
[0.422s] Installing mission_control_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
[0.422s] writing list of installed files to '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log'
[0.436s] Invoked command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
