[ 42%] Built target ugv_sdk
[ 57%] Built target demo_bunker_robot
[ 57%] Built target demo_tracer_robot
[ 64%] Built target demo_ranger_robot
[ 75%] Built target demo_scout_robot
[ 78%] Built target demo_protocol_detector
[ 85%] Built target demo_scout_mini_omni_robot
[100%] Built target demo_hunter_robot
[100%] Built target demo_robot_version
-- Install configuration: "Release"
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/libugv_sdk.a
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/robot_common_interface.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/agilex_message.h
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/scout_interface.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/agilex_types.h
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/ranger_interface.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/tracer_interface.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/titan_interface.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/bunker_interface.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/hunter_interface.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port/async_serial.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port/async_can.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port/ring_buffer.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v2
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v2/protocol_v2_parser.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/bunker_base.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/titan_base.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/agilex_base.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/hunter_base.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/ranger_base.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/tracer_base.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/scout_base.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1/protocol_v1_parser.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1/robot_limits.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1/agilex_msg_parser_v1.h
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/parser_base.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/utilities
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/utilities/protocol_detector.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/titan_robot.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/ranger_robot.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/tracer_robot.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/hunter_robot.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/bunker_robot.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/scout_robot.hpp
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkTargets.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkTargets-release.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkConfig.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkConfigVersion.cmake
