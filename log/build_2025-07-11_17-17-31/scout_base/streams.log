[0.008s] Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
[0.059s] [100%] Built target scout_base_node
[0.067s] Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
[0.068s] Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
[0.075s] -- Install configuration: ""
[0.075s] -- Execute custom install script
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/lib/scout_base/scout_base_node
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_base.launch.py
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_mini_base.launch.py
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_mini_omni_base.launch.py
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/package_run_dependencies/scout_base
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/parent_prefix_path/scout_base
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/ament_prefix_path.sh
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/ament_prefix_path.dsv
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/path.sh
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/path.dsv
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.bash
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.sh
[0.076s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.zsh
[0.077s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.dsv
[0.077s] -- Symlinking: /home/<USER>/ros_ws/install/scout_base/share/scout_base/package.dsv
[0.085s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/packages/scout_base
[0.085s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/cmake/scout_baseConfig.cmake
[0.085s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/cmake/scout_baseConfig-version.cmake
[0.085s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/package.xml
[0.086s] Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
