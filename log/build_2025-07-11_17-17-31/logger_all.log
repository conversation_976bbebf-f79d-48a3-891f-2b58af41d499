[0.052s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.053s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7acf26026ec0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7acf261e0520>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7acf261e0520>>)
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.128s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros_ws'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore_ament_install'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_pkg']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_pkg'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_meta']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_meta'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ros']
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ros'
[0.137s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ardupilot_mavros_bridge' with type 'ros.ament_python' and name 'ardupilot_mavros_bridge'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ignore', 'ignore_ament_install']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore_ament_install'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_pkg']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_pkg'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_meta']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_meta'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ros']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ros'
[0.138s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_msg' with type 'ros.ament_cmake' and name 'rslidar_msg'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore_ament_install'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_pkg']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_pkg'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_meta']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_meta'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ros']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ros'
[0.141s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_sdk' with type 'ros.ament_cmake' and name 'rslidar_sdk'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'ignore'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'ignore_ament_install'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['colcon_pkg']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'colcon_pkg'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['colcon_meta']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'colcon_meta'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['ros']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'ros'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['cmake', 'python']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'cmake'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'python'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['python_setup_py']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'python_setup_py'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['ignore', 'ignore_ament_install']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'ignore'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'ignore_ament_install'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['colcon_pkg']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'colcon_pkg'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['colcon_meta']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'colcon_meta'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['ros']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'ros'
[0.142s] DEBUG:colcon.colcon_core.package_identification:Package 'src/scout_ros2/scout_base' with type 'ros.ament_cmake' and name 'scout_base'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'ignore_ament_install'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['colcon_pkg']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'colcon_pkg'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['colcon_meta']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'colcon_meta'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['ros']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'ros'
[0.143s] DEBUG:colcon.colcon_core.package_identification:Package 'src/scout_ros2/scout_description' with type 'ros.ament_cmake' and name 'scout_description'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'ignore'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'ignore_ament_install'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['colcon_pkg']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'colcon_pkg'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['colcon_meta']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'colcon_meta'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['ros']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'ros'
[0.143s] DEBUG:colcon.colcon_core.package_identification:Package 'src/scout_ros2/scout_msgs' with type 'ros.ament_cmake' and name 'scout_msgs'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'ignore'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'ignore_ament_install'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['colcon_pkg']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'colcon_pkg'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['colcon_meta']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'colcon_meta'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['ros']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'ros'
[0.144s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ugv_sdk' with type 'ros.catkin' and name 'ugv_sdk'
[0.144s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.144s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.144s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.144s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.144s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.161s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.161s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.163s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 324 installed packages in /opt/ros/humble
[0.164s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.184s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_args' from command line to 'None'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target' from command line to 'None'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_first' from command line to 'False'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_force_configure' from command line to 'False'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'ament_cmake_args' from command line to 'None'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.184s] DEBUG:colcon.colcon_core.verb:Building package 'ardupilot_mavros_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge', 'symlink_install': False, 'test_result_base': None}
[0.184s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_args' from command line to 'None'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target' from command line to 'None'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_cache' from command line to 'False'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_first' from command line to 'False'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_force_configure' from command line to 'False'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'ament_cmake_args' from command line to 'None'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_cmake_args' from command line to 'None'
[0.184s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.184s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_msg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_msg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_msg', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_msg', 'symlink_install': False, 'test_result_base': None}
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_target' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_clean_first' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_force_configure' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'ament_cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.185s] DEBUG:colcon.colcon_core.verb:Building package 'scout_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/scout_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/scout_description', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/scout_ros2/scout_description', 'symlink_install': False, 'test_result_base': None}
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_target' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.185s] DEBUG:colcon.colcon_core.verb:Building package 'scout_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/scout_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/scout_msgs', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs', 'symlink_install': False, 'test_result_base': None}
[0.185s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_target' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.185s] DEBUG:colcon.colcon_core.verb:Building package 'ugv_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/ugv_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/ugv_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/ugv_sdk', 'symlink_install': False, 'test_result_base': None}
[0.185s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.185s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.186s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_sdk', 'symlink_install': False, 'test_result_base': None}
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_target' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_clean_cache' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_clean_first' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_force_configure' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'ament_cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'catkin_cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.186s] DEBUG:colcon.colcon_core.verb:Building package 'scout_base' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/scout_base', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/scout_base', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/scout_ros2/scout_base', 'symlink_install': False, 'test_result_base': None}
[0.186s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.187s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.187s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_msg' with build type 'ament_cmake'
[0.187s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_msg'
[0.188s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.188s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.188s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.190s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs' with build type 'ament_cmake'
[0.190s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs'
[0.190s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.190s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.191s] INFO:colcon.colcon_ros.task.catkin.build:Building ROS package in '/home/<USER>/ros_ws/src/ugv_sdk' with build type 'catkin'
[0.191s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/ugv_sdk'
[0.192s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.192s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.193s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' with build type 'ament_python'
[0.193s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'ament_prefix_path')
[0.193s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.ps1'
[0.195s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.dsv'
[0.196s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.sh'
[0.196s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.196s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.197s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/scout_ros2/scout_description' with build type 'ament_cmake'
[0.197s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/scout_ros2/scout_description'
[0.197s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.197s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.201s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.203s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_msgs -- -j16 -l16
[0.204s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake --build /home/<USER>/ros_ws/build/ugv_sdk -- -j16 -l16
[0.400s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge'
[0.401s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.401s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.412s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
[0.412s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake --build /home/<USER>/ros_ws/build/ugv_sdk -- -j16 -l16
[0.419s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake --install /home/<USER>/ros_ws/build/ugv_sdk
[0.428s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake --install /home/<USER>/ros_ws/build/ugv_sdk
[0.429s] Level 1:colcon.colcon_core.shell:create_environment_hook('ugv_sdk', 'ros_package_path')
[0.429s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/ros_package_path.ps1'
[0.430s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/ros_package_path.dsv'
[0.430s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/ros_package_path.sh'
[0.431s] Level 1:colcon.colcon_core.shell:create_environment_hook('ugv_sdk', 'pkg_config_path')
[0.431s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path.ps1'
[0.432s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path.dsv'
[0.432s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path.sh'
[0.434s] Level 1:colcon.colcon_core.shell:create_environment_hook('ugv_sdk', 'pkg_config_path_multiarch')
[0.434s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path_multiarch.ps1'
[0.435s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path_multiarch.dsv'
[0.436s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path_multiarch.sh'
[0.437s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ugv_sdk)
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk' for CMake module files
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk' for CMake config files
[0.440s] Level 1:colcon.colcon_core.shell:create_environment_hook('ugv_sdk', 'cmake_prefix_path')
[0.441s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/cmake_prefix_path.ps1'
[0.441s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/cmake_prefix_path.dsv'
[0.442s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/cmake_prefix_path.sh'
[0.442s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/lib'
[0.443s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/bin'
[0.443s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig/ugv_sdk.pc'
[0.443s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/lib/python3.10/site-packages'
[0.443s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/bin'
[0.443s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.ps1'
[0.444s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.dsv'
[0.444s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.sh'
[0.445s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.bash'
[0.445s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.zsh'
[0.445s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/ugv_sdk/share/colcon-core/packages/ugv_sdk)
[0.447s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
[0.448s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
[0.448s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.450s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[0.461s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_msgs' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_msgs -- -j16 -l16
[0.462s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_msgs
[0.463s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_description)
[0.463s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake module files
[0.464s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
[0.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake config files
[0.464s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_description', 'cmake_prefix_path')
[0.464s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.ps1'
[0.465s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.dsv'
[0.465s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.sh'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/pkgconfig/scout_description.pc'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/python3.10/site-packages'
[0.466s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[0.466s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.ps1'
[0.466s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.dsv'
[0.466s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.sh'
[0.466s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.bash'
[0.467s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.zsh'
[0.467s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_description/share/colcon-core/packages/scout_description)
[0.468s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_description)
[0.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake module files
[0.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake config files
[0.469s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_description', 'cmake_prefix_path')
[0.469s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.ps1'
[0.469s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.dsv'
[0.470s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.sh'
[0.470s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[0.470s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/pkgconfig/scout_description.pc'
[0.470s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/python3.10/site-packages'
[0.470s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[0.471s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.ps1'
[0.471s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.dsv'
[0.471s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.sh'
[0.472s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.bash'
[0.473s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.zsh'
[0.474s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_description/share/colcon-core/packages/scout_description)
[0.493s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[0.493s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[0.494s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[0.495s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[0.495s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[0.496s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[0.496s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[0.496s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[0.496s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[0.496s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[0.496s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[0.497s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.497s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[0.497s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[0.498s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[0.498s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[0.498s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[0.498s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[0.498s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[0.499s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[0.499s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[0.499s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[0.500s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[0.500s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[0.500s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[0.500s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[0.500s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[0.500s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.501s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[0.501s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[0.502s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[0.502s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[0.502s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[0.502s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[0.502s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_sdk' with build type 'ament_cmake'
[0.502s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_sdk'
[0.503s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.503s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.511s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_msgs)
[0.512s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs' for CMake module files
[0.512s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.514s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs' for CMake config files
[0.514s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_msgs' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_msgs
[0.515s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_msgs', 'cmake_prefix_path')
[0.515s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.ps1'
[0.515s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.dsv'
[0.516s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.sh'
[0.516s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib'
[0.516s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_msgs', 'ld_library_path_lib')
[0.517s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.ps1'
[0.517s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.dsv'
[0.517s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.sh'
[0.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/bin'
[0.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib/pkgconfig/scout_msgs.pc'
[0.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib/python3.10/site-packages'
[0.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/bin'
[0.519s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.ps1'
[0.519s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.dsv'
[0.520s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.sh'
[0.520s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.bash'
[0.520s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.zsh'
[0.521s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_msgs/share/colcon-core/packages/scout_msgs)
[0.521s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_msgs)
[0.521s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs' for CMake module files
[0.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs' for CMake config files
[0.523s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_msgs', 'cmake_prefix_path')
[0.523s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.ps1'
[0.523s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.dsv'
[0.524s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.sh'
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib'
[0.524s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_msgs', 'ld_library_path_lib')
[0.524s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.ps1'
[0.525s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.dsv'
[0.525s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.sh'
[0.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/bin'
[0.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib/pkgconfig/scout_msgs.pc'
[0.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib/python3.10/site-packages'
[0.526s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/bin'
[0.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.ps1'
[0.526s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.dsv'
[0.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.sh'
[0.527s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.bash'
[0.527s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.zsh'
[0.527s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_msgs/share/colcon-core/packages/scout_msgs)
[0.527s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/scout_ros2/scout_base' with build type 'ament_cmake'
[0.527s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/scout_ros2/scout_base'
[0.527s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.527s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.536s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
[0.562s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.564s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.583s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[0.583s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[0.584s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.584s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[0.585s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[0.585s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[0.585s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[0.586s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[0.586s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[0.586s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.586s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[0.587s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[0.587s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.587s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[0.588s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[0.588s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[0.588s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[0.588s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[0.588s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[0.589s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[0.589s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[0.589s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[0.589s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[0.589s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[0.589s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[0.590s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[0.590s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[0.590s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.590s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[0.590s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[0.590s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.590s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[0.591s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[0.591s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[0.591s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[0.591s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[0.591s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[0.595s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
[0.596s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
[0.614s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_base)
[0.614s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base' for CMake module files
[0.615s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
[0.615s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base' for CMake config files
[0.615s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_base', 'cmake_prefix_path')
[0.615s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.ps1'
[0.615s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.dsv'
[0.616s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.sh'
[0.616s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib'
[0.616s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/bin'
[0.616s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib/pkgconfig/scout_base.pc'
[0.616s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib/python3.10/site-packages'
[0.616s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/bin'
[0.616s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.ps1'
[0.617s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.dsv'
[0.617s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.sh'
[0.617s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.bash'
[0.617s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.zsh'
[0.617s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_base/share/colcon-core/packages/scout_base)
[0.618s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_base)
[0.618s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base' for CMake module files
[0.618s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base' for CMake config files
[0.618s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_base', 'cmake_prefix_path')
[0.618s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.ps1'
[0.618s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.dsv'
[0.618s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.sh'
[0.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib'
[0.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/bin'
[0.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib/pkgconfig/scout_base.pc'
[0.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib/python3.10/site-packages'
[0.619s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/bin'
[0.619s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.ps1'
[0.619s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.dsv'
[0.620s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.sh'
[0.620s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.bash'
[0.620s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.zsh'
[0.620s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_base/share/colcon-core/packages/scout_base)
[0.622s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
[0.767s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake module files
[0.767s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
[0.767s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake config files
[0.768s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib'
[0.768s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.768s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/pkgconfig/ardupilot_mavros_bridge.pc'
[0.768s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages'
[0.768s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'pythonpath')
[0.768s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.ps1'
[0.768s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.dsv'
[0.769s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.sh'
[0.769s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.769s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ardupilot_mavros_bridge)
[0.769s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.ps1'
[0.769s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.dsv'
[0.769s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.sh'
[0.770s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.bash'
[0.770s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.zsh'
[0.770s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/colcon-core/packages/ardupilot_mavros_bridge)
[0.770s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.770s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.770s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.770s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.773s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.773s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.774s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.780s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.781s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.ps1'
[0.782s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_ps1.py'
[0.784s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.ps1'
[0.785s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.sh'
[0.785s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_sh.py'
[0.786s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.sh'
[0.786s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.bash'
[0.787s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.bash'
[0.787s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.zsh'
[0.787s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.zsh'
