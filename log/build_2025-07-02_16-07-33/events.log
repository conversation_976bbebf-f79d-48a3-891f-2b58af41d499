[0.000000] (-) TimerEvent: {}
[0.000232] (ardupilot_mavros_bridge) JobQueued: {'identifier': 'ardupilot_mavros_bridge', 'dependencies': OrderedDict()}
[0.000500] (rslidar_msg) JobQueued: {'identifier': 'rslidar_msg', 'dependencies': OrderedDict()}
[0.000593] (rslidar_sdk) JobQueued: {'identifier': 'rslidar_sdk', 'dependencies': OrderedDict([('rslidar_msg', '/home/<USER>/ros_ws/install/rslidar_msg')])}
[0.000666] (rslidar_msg) JobStarted: {'identifier': 'rslidar_msg'}
[0.003530] (ardupilot_mavros_bridge) JobStarted: {'identifier': 'ardupilot_mavros_bridge'}
[0.006949] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'cmake'}
[0.007469] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/rslidar_msg', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 64981 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-6e83e1bf-7818-4dc1-9b17-b7dad4a27528.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '18'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 64981 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble')]), 'shell': False}
[0.020216] (rslidar_msg) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.098377] (rslidar_msg) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.099824] (-) TimerEvent: {}
[0.102037] (rslidar_msg) StdoutLine: {'line': b'-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)\n'}
[0.115132] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.116467] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.119553] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.124612] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.131575] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.143828] (rslidar_msg) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.157668] (rslidar_msg) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.158427] (rslidar_msg) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.199909] (-) TimerEvent: {}
[0.221314] (rslidar_msg) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.241715] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.300009] (-) TimerEvent: {}
[0.302400] (ardupilot_mavros_bridge) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/ardupilot_mavros_bridge', 'build', '--build-base', '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build', 'install', '--record', '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:en_US:en', 'USER': 'arduonboard', 'SSH_CLIENT': '************* 64981 22', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh', 'MOTD_SHOWN': 'pam', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '0', 'OLDPWD': '/home/<USER>', 'TERM_PROGRAM_VERSION': '1.101.2', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-6e83e1bf-7818-4dc1-9b17-b7dad4a27528.sock', 'ROS_PYTHON_VERSION': '3', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/certs/ca-certificates.crt', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', '_CE_M': '', 'COLCON_PREFIX_PATH': '/home/<USER>/ros_ws/install', 'ROS_DISTRO': 'humble', 'LOGNAME': 'arduonboard', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '18', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'PATH': '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'a4', 'LC_ADDRESS': 'zh_CN.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'MAMBA_ROOT_PREFIX': '/home/<USER>/miniforge3', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt', 'LANG': 'zh_CN.UTF-8', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-52fd21289e.sock', 'TERM_PROGRAM': 'vscode', 'AMENT_PREFIX_PATH': '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/miniforge3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'MAMBA_EXE': '/home/<USER>/miniforge3/bin/mamba', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'PWD': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'CONDA_EXE': '/home/<USER>/miniforge3/bin/conda', 'SSH_CONNECTION': '************* 64981 ************* 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg'}, 'shell': False}
[0.400191] (-) TimerEvent: {}
[0.408619] (rslidar_msg) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[0.423449] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running egg_info\n'}
[0.423716] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/PKG-INFO\n'}
[0.423917] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing dependency_links to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/dependency_links.txt\n'}
[0.423982] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing entry points to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/entry_points.txt\n'}
[0.424016] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing requirements to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/requires.txt\n'}
[0.424082] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing top-level names to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/top_level.txt\n'}
[0.424816] (ardupilot_mavros_bridge) StdoutLine: {'line': b"reading manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'\n"}
[0.425455] (ardupilot_mavros_bridge) StdoutLine: {'line': b"writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'\n"}
[0.425491] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running build\n'}
[0.425518] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running build_py\n'}
[0.425554] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install\n'}
[0.425726] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_lib\n'}
[0.426395] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_data\n'}
[0.426438] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying config/apm_config.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config\n'}
[0.426523] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying config/apm_pluginlists.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config\n'}
[0.426566] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_egg_info\n'}
[0.427581] (ardupilot_mavros_bridge) StdoutLine: {'line': b"removing '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info' (and everything under it)\n"}
[0.427634] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Copying ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info\n'}
[0.428015] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_scripts\n'}
[0.439774] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge\n'}
[0.439833] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Installing mission_control_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge\n'}
[0.440002] (ardupilot_mavros_bridge) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log'\n"}
[0.455617] (ardupilot_mavros_bridge) CommandEnded: {'returncode': 0}
[0.464297] (ardupilot_mavros_bridge) JobEnded: {'identifier': 'ardupilot_mavros_bridge', 'rc': 0}
[0.500387] (-) TimerEvent: {}
[0.600638] (-) TimerEvent: {}
[0.641540] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.700794] (-) TimerEvent: {}
[0.731065] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.744537] (rslidar_msg) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[0.758534] (rslidar_msg) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[0.758642] (rslidar_msg) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[0.758711] (rslidar_msg) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so\n'}
[0.773118] (rslidar_msg) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.777964] (rslidar_msg) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[0.780436] (rslidar_msg) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.781833] (rslidar_msg) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.782952] (rslidar_msg) StdoutLine: {'line': b'-- Configuring done\n'}
[0.798211] (rslidar_msg) StdoutLine: {'line': b'-- Generating done\n'}
[0.800872] (-) TimerEvent: {}
[0.804460] (rslidar_msg) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_msg\n'}
[0.809957] (rslidar_msg) CommandEnded: {'returncode': 0}
[0.810477] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'build'}
[0.811306] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/rslidar_msg', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 64981 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-6e83e1bf-7818-4dc1-9b17-b7dad4a27528.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '18'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 64981 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble')]), 'shell': False}
[0.843931] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_c\x1b[0m\n'}
[0.852449] (rslidar_msg) StdoutLine: {'line': b'[  3%] Built target rslidar_msg__cpp\n'}
[0.853038] (rslidar_msg) StdoutLine: {'line': b'[ 12%] Built target rslidar_msg__rosidl_generator_c\n'}
[0.857475] (rslidar_msg) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_symlink_rslidar_msg\n'}
[0.858554] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_cpp\x1b[0m\n'}
[0.859209] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c\x1b[0m\n'}
[0.859323] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c\x1b[0m\n'}
[0.861423] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_cpp\x1b[0m\n'}
[0.861617] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_cpp\x1b[0m\n'}
[0.862802] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c\x1b[0m\n'}
[0.865788] (rslidar_msg) StdoutLine: {'line': b'[ 25%] Built target rslidar_msg__rosidl_typesupport_introspection_cpp\n'}
[0.866047] (rslidar_msg) StdoutLine: {'line': b'[ 32%] Built target rslidar_msg__rosidl_typesupport_c\n'}
[0.868523] (rslidar_msg) StdoutLine: {'line': b'[ 41%] Built target rslidar_msg__rosidl_typesupport_introspection_c\n'}
[0.870321] (rslidar_msg) StdoutLine: {'line': b'[ 51%] Built target rslidar_msg__rosidl_typesupport_fastrtps_cpp\n'}
[0.871764] (rslidar_msg) StdoutLine: {'line': b'[ 61%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c\n'}
[0.872348] (rslidar_msg) StdoutLine: {'line': b'[ 70%] Built target rslidar_msg__rosidl_typesupport_cpp\n'}
[0.888492] (rslidar_msg) StdoutLine: {'line': b'[ 70%] Built target rslidar_msg\n'}
[0.900977] (-) TimerEvent: {}
[0.902452] (rslidar_msg) StdoutLine: {'line': b'[ 74%] Built target rslidar_msg__py\n'}
[0.911799] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_py\x1b[0m\n'}
[0.920252] (rslidar_msg) StdoutLine: {'line': b'[ 80%] Built target rslidar_msg__rosidl_generator_py\n'}
[0.929726] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext\x1b[0m\n'}
[0.929906] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c__pyext\x1b[0m\n'}
[0.929987] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c__pyext\x1b[0m\n'}
[0.942642] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target rslidar_msg__rosidl_typesupport_introspection_c__pyext\n'}
[0.942847] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target rslidar_msg__rosidl_typesupport_c__pyext\n'}
[0.942937] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.984714] (rslidar_msg) StdoutLine: {'line': b'running egg_info\n'}
[0.985060] (rslidar_msg) StdoutLine: {'line': b'writing rslidar_msg.egg-info/PKG-INFO\n'}
[0.985257] (rslidar_msg) StdoutLine: {'line': b'writing dependency_links to rslidar_msg.egg-info/dependency_links.txt\n'}
[0.985343] (rslidar_msg) StdoutLine: {'line': b'writing top-level names to rslidar_msg.egg-info/top_level.txt\n'}
[0.986290] (rslidar_msg) StdoutLine: {'line': b"reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'\n"}
[0.986532] (rslidar_msg) StdoutLine: {'line': b"writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'\n"}
[1.001200] (-) TimerEvent: {}
[1.004584] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_rslidar_msg_egg\n'}
[1.013237] (rslidar_msg) CommandEnded: {'returncode': 0}
[1.014143] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'install'}
[1.020887] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/rslidar_msg'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 64981 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-6e83e1bf-7818-4dc1-9b17-b7dad4a27528.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '18'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 64981 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble')]), 'shell': False}
[1.027555] (rslidar_msg) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.027867] (rslidar_msg) StdoutLine: {'line': b'-- Execute custom install script\n'}
[1.028020] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/rosidl_interfaces/rslidar_msg\n'}
[1.028204] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.h\n'}
[1.028313] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.h\n'}
[1.028406] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.h\n'}
[1.028474] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_c__visibility_control.h\n'}
[1.028541] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.h\n'}
[1.028642] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.sh\n'}
[1.028734] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.dsv\n'}
[1.028804] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_c.h\n'}
[1.028889] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[1.028976] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__builder.hpp\n'}
[1.029061] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.hpp\n'}
[1.029154] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__traits.hpp\n'}
[1.029220] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.hpp\n'}
[1.029283] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[1.029350] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.hpp\n'}
[1.029435] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[1.029502] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[1.029586] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_c.h\n'}
[1.029722] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[1.030135] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_cpp.hpp\n'}
[1.030235] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.sh\n'}
[1.030321] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.dsv\n'}
[1.030404] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[1.030490] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[1.030575] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[1.030667] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/top_level.txt\n'}
[1.030747] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py\n'}
[1.030822] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_c.c\n'}
[1.030907] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[1.031007] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_introspection_c.c\n'}
[1.031075] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/librslidar_msg__rosidl_generator_py.so\n'}
[1.031164] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/__init__.py\n'}
[1.031232] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet.py\n'}
[1.031299] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet_s.c\n'}
[1.031367] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.031434] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.031536] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.031622] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.031715] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.031785] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.031852] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.idl\n'}
[1.031919] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.msg\n'}
[1.031984] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/package_run_dependencies/rslidar_msg\n'}
[1.032068] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/parent_prefix_path/rslidar_msg\n'}
[1.032173] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.sh\n'}
[1.032240] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.dsv\n'}
[1.032308] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.sh\n'}
[1.032376] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.dsv\n'}
[1.032442] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.bash\n'}
[1.032509] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.sh\n'}
[1.032576] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.zsh\n'}
[1.032674] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.dsv\n'}
[1.032739] (rslidar_msg) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv\n'}
[1.038148] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/packages/rslidar_msg\n'}
[1.038290] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake-extras.cmake\n'}
[1.038381] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[1.038450] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[1.038518] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[1.038616] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_targets-extras.cmake\n'}
[1.038708] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[1.038773] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[1.038842] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig.cmake\n'}
[1.038909] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig-version.cmake\n'}
[1.038996] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.xml\n'}
[1.039064] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so\n'}
[1.039131] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so\n'}
[1.039233] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so\n'}
[1.039302] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so\n'}
[1.039368] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so\n'}
[1.039460] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so\n'}
[1.039529] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so\n'}
[1.058911] (rslidar_msg) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg'...\n"}
[1.059037] (rslidar_msg) StdoutLine: {'line': b"Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py'...\n"}
[1.059105] (rslidar_msg) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg'...\n"}
[1.061974] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so\n'}
[1.062181] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport.cmake\n'}
[1.062261] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport-noconfig.cmake\n'}
[1.062327] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[1.062388] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[1.062450] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cppExport.cmake\n'}
[1.062594] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[1.062695] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[1.062765] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport.cmake\n'}
[1.062828] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[1.062888] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport.cmake\n'}
[1.062947] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport-noconfig.cmake\n'}
[1.063015] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport.cmake\n'}
[1.063082] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[1.063141] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport.cmake\n'}
[1.063199] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[1.063256] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport.cmake\n'}
[1.063311] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport-noconfig.cmake\n'}
[1.063891] (rslidar_msg) CommandEnded: {'returncode': 0}
[1.078844] (rslidar_msg) JobEnded: {'identifier': 'rslidar_msg', 'rc': 0}
[1.079559] (rslidar_sdk) JobStarted: {'identifier': 'rslidar_sdk'}
[1.086047] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'cmake'}
[1.086788] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/rslidar_sdk', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 64981 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-6e83e1bf-7818-4dc1-9b17-b7dad4a27528.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '18'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 64981 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble')]), 'shell': False}
[1.096622] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.096770] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- POINT_TYPE is XYZI\x1b[0m\n'}
[1.096803] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.101252] (-) TimerEvent: {}
[1.101393] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.101435] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- ROS Not Found. ROS Support is turned Off.\x1b[0m\n'}
[1.101465] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.172935] (rslidar_sdk) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[1.191845] (rslidar_sdk) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.193102] (rslidar_sdk) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.196100] (rslidar_sdk) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[1.201052] (rslidar_sdk) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.201311] (-) TimerEvent: {}
[1.207675] (rslidar_sdk) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.224638] (rslidar_sdk) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.225309] (rslidar_sdk) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.288755] (rslidar_sdk) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.301508] (-) TimerEvent: {}
[1.314573] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.314693] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- ROS2 Found. ROS2 Support is turned On.\x1b[0m\n'}
[1.314724] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.314750] (rslidar_sdk) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[1.330032] (rslidar_sdk) StdoutLine: {'line': b'-- Found rslidar_msg: 0.0.0 (/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake)\n'}
[1.337884] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.337959] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- CMake run for UNIX GNU Compiler\x1b[0m\n'}
[1.337989] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.338103] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.338162] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- rs_driver Version : v1.5.17\x1b[0m\n'}
[1.338195] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.384784] (rslidar_sdk) StdoutLine: {'line': b'-- Configuring done\n'}
[1.390045] (rslidar_sdk) StdoutLine: {'line': b'-- Generating done\n'}
[1.392007] (rslidar_sdk) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_sdk\n'}
[1.397755] (rslidar_sdk) CommandEnded: {'returncode': 0}
[1.397989] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'build'}
[1.398004] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/rslidar_sdk', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 64981 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-6e83e1bf-7818-4dc1-9b17-b7dad4a27528.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '18'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 64981 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble')]), 'shell': False}
[1.401627] (-) TimerEvent: {}
[1.430673] (rslidar_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_sdk_node\x1b[0m\n'}
[1.444126] (rslidar_sdk) StdoutLine: {'line': b'[100%] Built target rslidar_sdk_node\n'}
[1.451472] (rslidar_sdk) CommandEnded: {'returncode': 0}
[1.452392] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'install'}
[1.452855] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/rslidar_sdk'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 64981 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-6e83e1bf-7818-4dc1-9b17-b7dad4a27528.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('COLCON_PREFIX_PATH', '/home/<USER>/ros_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '18'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 64981 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble')]), 'shell': False}
[1.459446] (rslidar_sdk) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[1.459930] (rslidar_sdk) StdoutLine: {'line': b'-- Execute custom install script\n'}
[1.460197] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node\n'}
[1.460514] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py\n'}
[1.460617] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch\n'}
[1.460805] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py\n'}
[1.460994] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz\n'}
[1.461029] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz\n'}
[1.461073] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk\n'}
[1.461127] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk\n'}
[1.461205] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh\n'}
[1.461241] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv\n'}
[1.461280] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh\n'}
[1.461335] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv\n'}
[1.461392] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash\n'}
[1.461447] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh\n'}
[1.461501] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh\n'}
[1.461553] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv\n'}
[1.461594] (rslidar_sdk) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv\n'}
[1.466828] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk\n'}
[1.466909] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake\n'}
[1.466937] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake\n'}
[1.466981] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml\n'}
[1.468328] (rslidar_sdk) CommandEnded: {'returncode': 0}
[1.479582] (rslidar_sdk) JobEnded: {'identifier': 'rslidar_sdk', 'rc': 0}
[1.479934] (-) EventReactorShutdown: {}
