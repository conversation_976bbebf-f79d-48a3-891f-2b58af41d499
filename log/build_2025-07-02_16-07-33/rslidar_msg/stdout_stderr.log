-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_msg
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_c[0m
[  3%] Built target rslidar_msg__cpp
[ 12%] Built target rslidar_msg__rosidl_generator_c
[ 12%] Built target ament_cmake_python_symlink_rslidar_msg
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_cpp[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_cpp[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_cpp[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c[0m
[ 25%] Built target rslidar_msg__rosidl_typesupport_introspection_cpp
[ 32%] Built target rslidar_msg__rosidl_typesupport_c
[ 41%] Built target rslidar_msg__rosidl_typesupport_introspection_c
[ 51%] Built target rslidar_msg__rosidl_typesupport_fastrtps_cpp
[ 61%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c
[ 70%] Built target rslidar_msg__rosidl_typesupport_cpp
[ 70%] Built target rslidar_msg
[ 74%] Built target rslidar_msg__py
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_py[0m
[ 80%] Built target rslidar_msg__rosidl_generator_py
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c__pyext[0m
[35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c__pyext[0m
[100%] Built target rslidar_msg__rosidl_typesupport_introspection_c__pyext
[100%] Built target rslidar_msg__rosidl_typesupport_c__pyext
[100%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext
running egg_info
writing rslidar_msg.egg-info/PKG-INFO
writing dependency_links to rslidar_msg.egg-info/dependency_links.txt
writing top-level names to rslidar_msg.egg-info/top_level.txt
reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'
writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[100%] Built target ament_cmake_python_build_rslidar_msg_egg
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/rosidl_interfaces/rslidar_msg
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_c__visibility_control.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.sh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.dsv
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_c.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__builder.hpp
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.hpp
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__traits.hpp
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.hpp
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_cpp__visibility_control.hpp
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.hpp
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_cpp.hpp
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_c.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_introspection_c__visibility_control.h
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_cpp.hpp
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.sh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.dsv
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/PKG-INFO
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/SOURCES.txt
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/dependency_links.txt
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/top_level.txt
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_c.c
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_introspection_c.c
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/librslidar_msg__rosidl_generator_py.so
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/__init__.py
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet.py
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet_s.c
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.idl
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.msg
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/package_run_dependencies/rslidar_msg
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/parent_prefix_path/rslidar_msg
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.dsv
-- Symlinking: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/packages/rslidar_msg
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_include_directories-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_libraries-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.xml
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so
Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg'...
Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py'...
Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg'...
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport-noconfig.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cppExport.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport-noconfig.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport-noconfig.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport-noconfig.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport-noconfig.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport.cmake
-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport-noconfig.cmake
