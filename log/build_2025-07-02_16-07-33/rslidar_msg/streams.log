[0.009s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[0.020s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.098s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.101s] -- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
[0.114s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.116s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.119s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.124s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.131s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.143s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.157s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.158s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.221s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.241s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.408s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[0.641s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.730s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.744s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[0.758s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[0.758s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[0.758s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
[0.772s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.777s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.780s] -- Added test 'lint_cmake' to check CMake code style
[0.781s] -- Added test 'xmllint' to check XML markup files
[0.782s] -- Configuring done
[0.798s] -- Generating done
[0.804s] -- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_msg
[0.809s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[0.811s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.843s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_c[0m
[0.852s] [  3%] Built target rslidar_msg__cpp
[0.852s] [ 12%] Built target rslidar_msg__rosidl_generator_c
[0.857s] [ 12%] Built target ament_cmake_python_symlink_rslidar_msg
[0.858s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_cpp[0m
[0.859s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c[0m
[0.859s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c[0m
[0.861s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_cpp[0m
[0.861s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_cpp[0m
[0.862s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c[0m
[0.865s] [ 25%] Built target rslidar_msg__rosidl_typesupport_introspection_cpp
[0.865s] [ 32%] Built target rslidar_msg__rosidl_typesupport_c
[0.868s] [ 41%] Built target rslidar_msg__rosidl_typesupport_introspection_c
[0.870s] [ 51%] Built target rslidar_msg__rosidl_typesupport_fastrtps_cpp
[0.871s] [ 61%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c
[0.872s] [ 70%] Built target rslidar_msg__rosidl_typesupport_cpp
[0.888s] [ 70%] Built target rslidar_msg
[0.902s] [ 74%] Built target rslidar_msg__py
[0.911s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_py[0m
[0.920s] [ 80%] Built target rslidar_msg__rosidl_generator_py
[0.929s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext[0m
[0.929s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c__pyext[0m
[0.929s] [35m[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c__pyext[0m
[0.942s] [100%] Built target rslidar_msg__rosidl_typesupport_introspection_c__pyext
[0.942s] [100%] Built target rslidar_msg__rosidl_typesupport_c__pyext
[0.942s] [100%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext
[0.984s] running egg_info
[0.984s] writing rslidar_msg.egg-info/PKG-INFO
[0.985s] writing dependency_links to rslidar_msg.egg-info/dependency_links.txt
[0.985s] writing top-level names to rslidar_msg.egg-info/top_level.txt
[0.986s] reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[0.986s] writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[1.004s] [100%] Built target ament_cmake_python_build_rslidar_msg_egg
[1.013s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[1.021s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[1.027s] -- Install configuration: ""
[1.027s] -- Execute custom install script
[1.027s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/rosidl_interfaces/rslidar_msg
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.h
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.h
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.h
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_c__visibility_control.h
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.h
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.sh
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.dsv
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_c.h
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__builder.hpp
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.hpp
[1.028s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__traits.hpp
[1.029s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.hpp
[1.029s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_cpp__visibility_control.hpp
[1.029s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.hpp
[1.029s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_cpp.hpp
[1.029s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[1.029s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_c.h
[1.029s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_introspection_c__visibility_control.h
[1.029s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_cpp.hpp
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.sh
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.dsv
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/PKG-INFO
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/SOURCES.txt
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/dependency_links.txt
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/top_level.txt
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_c.c
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_introspection_c.c
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/librslidar_msg__rosidl_generator_py.so
[1.030s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/__init__.py
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet.py
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet_s.c
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.idl
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.msg
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/package_run_dependencies/rslidar_msg
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/parent_prefix_path/rslidar_msg
[1.031s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.sh
[1.032s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.dsv
[1.032s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.sh
[1.032s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.dsv
[1.032s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.bash
[1.032s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.sh
[1.032s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.zsh
[1.032s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.dsv
[1.032s] -- Symlinking: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/packages/rslidar_msg
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake-extras.cmake
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_dependencies-extras.cmake
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_include_directories-extras.cmake
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_libraries-extras.cmake
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_targets-extras.cmake
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig.cmake
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig-version.cmake
[1.038s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.xml
[1.038s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so
[1.038s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so
[1.039s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so
[1.039s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so
[1.039s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so
[1.039s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so
[1.039s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so
[1.058s] Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg'...
[1.058s] Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py'...
[1.058s] Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg'...
[1.061s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport-noconfig.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport-noconfig.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cppExport.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport-noconfig.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport-noconfig.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport-noconfig.cmake
[1.062s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport.cmake
[1.063s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport-noconfig.cmake
[1.063s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport.cmake
[1.063s] -- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport-noconfig.cmake
[1.064s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
