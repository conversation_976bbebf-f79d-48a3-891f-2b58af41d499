[0.008s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[0.017s] [0m=============================================================[0m
[0.017s] [0m-- POINT_TYPE is XYZI[0m
[0.017s] [0m=============================================================[0m
[0.022s] [0m=============================================================[0m
[0.022s] [0m-- ROS Not Found. ROS Support is turned Off.[0m
[0.022s] [0m=============================================================[0m
[0.093s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.112s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.114s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.117s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.122s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.128s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.145s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.146s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.209s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.235s] [0m=============================================================[0m
[0.235s] [0m-- ROS2 Found. ROS2 Support is turned On.[0m
[0.235s] [0m=============================================================[0m
[0.235s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.251s] -- Found rslidar_msg: 0.0.0 (/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake)
[0.258s] [0m=============================================================[0m
[0.258s] [0m-- CMake run for UNIX GNU Compiler[0m
[0.258s] [0m=============================================================[0m
[0.259s] [0m=============================================================[0m
[0.259s] [0m-- rs_driver Version : v1.5.17[0m
[0.259s] [0m=============================================================[0m
[0.305s] -- Configuring done
[0.311s] -- Generating done
[0.312s] -- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_sdk
[0.318s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[0.319s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.351s] [35m[1mConsolidate compiler generated dependencies of target rslidar_sdk_node[0m
[0.365s] [100%] Built target rslidar_sdk_node
[0.372s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.374s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.380s] -- Install configuration: "Release"
[0.380s] -- Execute custom install script
[0.381s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node
[0.381s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py
[0.381s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch
[0.381s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py
[0.381s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz
[0.381s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh
[0.382s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv
[0.382s] -- Symlinking: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv
[0.387s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk
[0.387s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake
[0.387s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake
[0.387s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml
[0.389s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
