[0.053s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.053s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7cbfc7f9b430>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7cbfc81ccbe0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7cbfc81ccbe0>>)
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.128s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.128s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros_ws'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.134s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore_ament_install'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_pkg']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_pkg'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_meta']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_meta'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ros']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ros'
[0.136s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ardupilot_mavros_bridge' with type 'ros.ament_python' and name 'ardupilot_mavros_bridge'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ignore', 'ignore_ament_install']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore_ament_install'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_pkg']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_pkg'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_meta']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_meta'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ros']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ros'
[0.137s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_msg' with type 'ros.ament_cmake' and name 'rslidar_msg'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore_ament_install'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_pkg']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_pkg'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_meta']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_meta'
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ros']
[0.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ros'
[0.141s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_sdk' with type 'ros.ament_cmake' and name 'rslidar_sdk'
[0.141s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.141s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.141s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.141s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.141s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.160s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 3 installed packages in /home/<USER>/ros_ws/install
[0.161s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 324 installed packages in /opt/ros/humble
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.181s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_first' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_force_configure' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'ament_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.181s] DEBUG:colcon.colcon_core.verb:Building package 'ardupilot_mavros_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge', 'symlink_install': False, 'test_result_base': None}
[0.181s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_cache' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_first' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_force_configure' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'ament_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.181s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_msg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_msg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_msg', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_msg', 'symlink_install': False, 'test_result_base': None}
[0.182s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.182s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_sdk', 'symlink_install': False, 'test_result_base': None}
[0.182s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.183s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.183s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_msg' with build type 'ament_cmake'
[0.183s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_msg'
[0.184s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.184s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.184s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.186s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' with build type 'ament_python'
[0.186s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'ament_prefix_path')
[0.186s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.ps1'
[0.186s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.dsv'
[0.187s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.sh'
[0.187s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.187s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.192s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[0.317s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge'
[0.318s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.318s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.486s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
[0.638s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
[0.640s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake module files
[0.641s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake config files
[0.641s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib'
[0.642s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.642s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/pkgconfig/ardupilot_mavros_bridge.pc'
[0.642s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages'
[0.642s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'pythonpath')
[0.642s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.ps1'
[0.643s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.dsv'
[0.643s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.sh'
[0.644s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.644s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ardupilot_mavros_bridge)
[0.644s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.ps1'
[0.645s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.dsv'
[0.645s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.sh'
[0.646s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.bash'
[0.646s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.zsh'
[0.646s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/colcon-core/packages/ardupilot_mavros_bridge)
[0.993s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[0.994s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[1.196s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[1.204s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[1.246s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[1.246s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[1.247s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[1.247s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[1.248s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[1.248s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[1.249s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[1.249s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[1.249s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[1.250s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[1.250s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[1.250s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[1.250s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[1.251s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[1.251s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[1.251s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[1.251s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[1.252s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[1.252s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[1.252s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[1.253s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[1.253s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[1.253s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[1.254s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[1.254s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[1.255s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[1.255s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[1.256s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[1.256s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[1.256s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[1.257s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[1.257s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[1.257s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[1.258s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[1.258s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[1.258s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[1.258s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[1.259s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[1.259s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[1.259s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[1.260s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[1.260s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[1.260s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[1.261s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[1.261s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[1.261s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_sdk' with build type 'ament_cmake'
[1.261s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_sdk'
[1.261s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.262s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.270s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[1.580s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[1.580s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[1.634s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[1.636s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[1.651s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[1.651s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[1.651s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[1.652s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[1.652s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[1.652s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[1.653s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[1.653s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[1.654s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[1.654s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[1.654s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[1.654s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[1.654s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[1.654s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[1.655s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[1.655s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[1.656s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[1.656s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[1.656s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[1.657s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[1.657s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[1.658s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[1.658s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[1.658s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[1.658s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[1.659s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[1.659s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[1.659s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[1.659s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[1.660s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[1.660s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[1.660s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[1.660s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[1.661s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[1.661s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[1.662s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[1.662s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[1.662s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.662s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.662s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.662s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.665s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.665s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.665s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.671s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.671s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.ps1'
[1.672s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_ps1.py'
[1.673s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.ps1'
[1.674s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.sh'
[1.675s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_sh.py'
[1.675s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.sh'
[1.676s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.bash'
[1.677s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.bash'
[1.677s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.zsh'
[1.678s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.zsh'
