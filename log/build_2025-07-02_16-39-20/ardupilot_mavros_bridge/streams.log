[0.287s] Invoking command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
[0.399s] running egg_info
[0.399s] writing ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/PKG-INFO
[0.399s] writing dependency_links to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/dependency_links.txt
[0.399s] writing entry points to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/entry_points.txt
[0.399s] writing requirements to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/requires.txt
[0.399s] writing top-level names to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/top_level.txt
[0.400s] reading manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.401s] writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.401s] running build
[0.401s] running build_py
[0.401s] running install
[0.401s] running install_lib
[0.402s] running install_data
[0.402s] error: can't copy 'launch/apm.launch.py': doesn't exist or not a regular file
[0.413s] Invoked command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' returned '1': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data
