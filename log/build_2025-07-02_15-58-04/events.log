[0.000000] (-) TimerEvent: {}
[0.000143] (-) JobUnselected: {'identifier': 'rslidar_msg'}
[0.000187] (-) JobUnselected: {'identifier': 'rslidar_sdk'}
[0.000235] (ardupilot_mavros_bridge) JobQueued: {'identifier': 'ardupilot_mavros_bridge', 'dependencies': OrderedDict()}
[0.000266] (ardupilot_mavros_bridge) JobStarted: {'identifier': 'ardupilot_mavros_bridge'}
[0.099544] (-) TimerEvent: {}
[0.199907] (-) TimerEvent: {}
[0.294942] (ardupilot_mavros_bridge) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/ardupilot_mavros_bridge', 'build', '--build-base', '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build', 'install', '--record', '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:en_US:en', 'USER': 'arduonboard', 'SSH_CLIENT': '************* 64981 22', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh', 'LESS': '-FX', 'MOTD_SHOWN': 'pam', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '0', 'OLDPWD': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge/launch', 'TERM_PROGRAM_VERSION': '1.101.2', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-f57ad4b9-b18c-4c46-a934-79339289e29d.sock', 'ROS_PYTHON_VERSION': '3', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/certs/ca-certificates.crt', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', '_CE_M': '', 'COLCON_PREFIX_PATH': '/home/<USER>/ros_ws/install', 'ROS_DISTRO': 'humble', 'LOGNAME': 'arduonboard', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '18', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'PATH': '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'a4', 'LC_ADDRESS': 'zh_CN.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'MAMBA_ROOT_PREFIX': '/home/<USER>/miniforge3', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'LANG': 'zh_CN.UTF-8', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-52fd21289e.sock', 'TERM_PROGRAM': 'vscode', 'AMENT_PREFIX_PATH': '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/miniforge3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'MAMBA_EXE': '/home/<USER>/miniforge3/bin/mamba', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'PWD': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'CONDA_EXE': '/home/<USER>/miniforge3/bin/conda', 'SSH_CONNECTION': '************* 64981 ************* 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/home/<USER>/ros_ws/build/ardupilot_mavros_bridge:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/rslidar_msg'}, 'shell': False}
[0.300005] (-) TimerEvent: {}
[0.400193] (-) TimerEvent: {}
[0.406760] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running egg_info\n'}
[0.407003] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/PKG-INFO\n'}
[0.407263] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing dependency_links to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/dependency_links.txt\n'}
[0.407338] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing entry points to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/entry_points.txt\n'}
[0.407480] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing requirements to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/requires.txt\n'}
[0.407520] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing top-level names to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/top_level.txt\n'}
[0.408297] (ardupilot_mavros_bridge) StdoutLine: {'line': b"reading manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'\n"}
[0.408794] (ardupilot_mavros_bridge) StdoutLine: {'line': b"writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'\n"}
[0.408855] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running build\n'}
[0.408907] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running build_py\n'}
[0.408932] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install\n'}
[0.409015] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_lib\n'}
[0.409643] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_data\n'}
[0.409694] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying launch/apm.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch\n'}
[0.409772] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying config/apm_config.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config\n'}
[0.409819] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_egg_info\n'}
[0.410913] (ardupilot_mavros_bridge) StdoutLine: {'line': b"removing '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info' (and everything under it)\n"}
[0.410940] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Copying ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info\n'}
[0.411142] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_scripts\n'}
[0.422825] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge\n'}
[0.422880] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Installing mission_control_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge\n'}
[0.423029] (ardupilot_mavros_bridge) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log'\n"}
[0.437387] (ardupilot_mavros_bridge) CommandEnded: {'returncode': 0}
[0.442417] (ardupilot_mavros_bridge) JobEnded: {'identifier': 'ardupilot_mavros_bridge', 'rc': 0}
[0.442724] (-) EventReactorShutdown: {}
