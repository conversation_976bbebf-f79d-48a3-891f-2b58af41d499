[0.011s] Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake /home/<USER>/ros_ws/src/ugv_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/ugv_sdk
[0.143s] -- Build package with cmake
[0.143s] -- Tests will not be built
[0.143s] -- Project will be installed to /home/<USER>/ros_ws/install/ugv_sdk with 'make install'
[0.143s] --  - To install LIB components to /home/<USER>/ros_ws/install/ugv_sdk/lib
[0.143s] --  - To install BIN components to /home/<USER>/ros_ws/install/ugv_sdk/bin
[0.143s] --  - To install INCLUDE components to /home/<USER>/ros_ws/install/ugv_sdk/include
[0.143s] --  - To install CMAKE components to /home/<USER>/ros_ws/install/ugv_sdk/share/cmake/ugv_sdk
[0.143s] -- Configuring done
[0.143s] -- Generating done
[0.143s] -- Build files have been written to: /home/<USER>/ros_ws/build/ugv_sdk
[0.146s] Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake /home/<USER>/ros_ws/src/ugv_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/ugv_sdk
[0.147s] Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake --build /home/<USER>/ros_ws/build/ugv_sdk -- -j16 -l16
[0.186s] [35m[1mConsolidate compiler generated dependencies of target ugv_sdk[0m
[0.214s] [ 42%] Built target ugv_sdk
[0.222s] [35m[1mConsolidate compiler generated dependencies of target demo_robot_version[0m
[0.222s] [35m[1mConsolidate compiler generated dependencies of target demo_protocol_detector[0m
[0.222s] [35m[1mConsolidate compiler generated dependencies of target demo_tracer_robot[0m
[0.222s] [35m[1mConsolidate compiler generated dependencies of target demo_scout_robot[0m
[0.224s] [35m[1mConsolidate compiler generated dependencies of target demo_bunker_robot[0m
[0.224s] [35m[1mConsolidate compiler generated dependencies of target demo_scout_mini_omni_robot[0m
[0.224s] [35m[1mConsolidate compiler generated dependencies of target demo_ranger_robot[0m
[0.224s] [35m[1mConsolidate compiler generated dependencies of target demo_hunter_robot[0m
[0.235s] [ 64%] Built target demo_bunker_robot
[0.235s] [ 64%] Built target demo_tracer_robot
[0.235s] [ 64%] Built target demo_protocol_detector
[0.239s] [ 71%] Built target demo_robot_version
[0.239s] [ 78%] Built target demo_scout_robot
[0.241s] [ 92%] Built target demo_hunter_robot
[0.241s] [ 92%] Built target demo_scout_mini_omni_robot
[0.242s] [100%] Built target demo_ranger_robot
[0.253s] Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake --build /home/<USER>/ros_ws/build/ugv_sdk -- -j16 -l16
[0.260s] Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake --install /home/<USER>/ros_ws/build/ugv_sdk
[0.264s] -- Install configuration: "Release"
[0.264s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/libugv_sdk.a
[0.264s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk
[0.264s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/robot_common_interface.hpp
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/agilex_message.h
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/scout_interface.hpp
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/agilex_types.h
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/ranger_interface.hpp
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/tracer_interface.hpp
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/titan_interface.hpp
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/bunker_interface.hpp
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/hunter_interface.hpp
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port/async_serial.hpp
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port/async_can.hpp
[0.265s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port/ring_buffer.hpp
[0.266s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v2
[0.266s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v2/protocol_v2_parser.hpp
[0.266s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base
[0.266s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/bunker_base.hpp
[0.266s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/titan_base.hpp
[0.267s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/agilex_base.hpp
[0.267s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/hunter_base.hpp
[0.267s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/ranger_base.hpp
[0.267s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/tracer_base.hpp
[0.268s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/scout_base.hpp
[0.268s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1
[0.268s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1/protocol_v1_parser.hpp
[0.268s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1/robot_limits.hpp
[0.269s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1/agilex_msg_parser_v1.h
[0.269s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/parser_base.hpp
[0.269s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/utilities
[0.269s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/utilities/protocol_detector.hpp
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/titan_robot.hpp
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/ranger_robot.hpp
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/tracer_robot.hpp
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/hunter_robot.hpp
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/bunker_robot.hpp
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/scout_robot.hpp
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkTargets.cmake
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkTargets-release.cmake
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkConfig.cmake
[0.270s] -- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkConfigVersion.cmake
[0.270s] Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake --install /home/<USER>/ros_ws/build/ugv_sdk
