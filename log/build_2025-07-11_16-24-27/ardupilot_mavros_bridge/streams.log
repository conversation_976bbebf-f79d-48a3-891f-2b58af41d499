[0.346s] Invoking command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --uninstall --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
[0.477s] running develop
[0.546s] Removing /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot-mavros-bridge.egg-link (link to .)
[0.567s] Invoked command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --uninstall --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
[0.568s] Invoking command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data --force
[0.692s] running egg_info
[0.692s] writing ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/PKG-INFO
[0.692s] writing dependency_links to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/dependency_links.txt
[0.692s] writing entry points to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/entry_points.txt
[0.693s] writing requirements to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/requires.txt
[0.693s] writing top-level names to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/top_level.txt
[0.693s] reading manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.694s] writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.694s] running build
[0.694s] running build_py
[0.694s] creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
[0.695s] creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib
[0.695s] creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.695s] copying ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.695s] copying ardupilot_mavros_bridge/mission_control_node.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.695s] copying ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.695s] running install
[0.695s] running install_lib
[0.695s] creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.695s] copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.695s] copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/mission_control_node.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.695s] copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.695s] byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/__init__.py to __init__.cpython-310.pyc
[0.696s] byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/mission_control_node.py to mission_control_node.cpython-310.pyc
[0.697s] byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/mavros_bridge_node.py to mavros_bridge_node.cpython-310.pyc
[0.698s] running install_data
[0.698s] copying resource/ardupilot_mavros_bridge -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ament_index/resource_index/packages
[0.698s] copying package.xml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge
[0.698s] copying launch/apm.launch -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.698s] copying launch/apm_optimized.launch -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.698s] copying launch/mavros_bridge.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.698s] copying launch/complete_system.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.699s] copying launch/navigation_system.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.699s] copying config/apm_config.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.699s] copying config/apm_config_optimized.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.699s] copying config/apm_pluginlists.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.699s] copying config/nav2_params.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.699s] running install_egg_info
[0.700s] Copying ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info
[0.700s] running install_scripts
[0.712s] Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
[0.712s] Installing mission_control_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
[0.713s] writing list of installed files to '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log'
[0.729s] Invoked command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data --force
