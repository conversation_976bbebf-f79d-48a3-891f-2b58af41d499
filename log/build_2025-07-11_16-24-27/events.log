[0.000000] (-) TimerEvent: {}
[0.000159] (ardupilot_mavros_bridge) JobQueued: {'identifier': 'ardupilot_mavros_bridge', 'dependencies': OrderedDict()}
[0.000192] (rslidar_msg) JobQueued: {'identifier': 'rslidar_msg', 'dependencies': OrderedDict()}
[0.000216] (scout_description) JobQueued: {'identifier': 'scout_description', 'dependencies': OrderedDict()}
[0.000235] (scout_msgs) JobQueued: {'identifier': 'scout_msgs', 'dependencies': OrderedDict()}
[0.000262] (ugv_sdk) JobQueued: {'identifier': 'ugv_sdk', 'dependencies': OrderedDict()}
[0.000287] (rslidar_sdk) JobQueued: {'identifier': 'rslidar_sdk', 'dependencies': OrderedDict([('rslidar_msg', '/home/<USER>/ros_ws/install/rslidar_msg')])}
[0.000339] (scout_base) JobQueued: {'identifier': 'scout_base', 'dependencies': OrderedDict([('scout_msgs', '/home/<USER>/ros_ws/install/scout_msgs'), ('ugv_sdk', '/home/<USER>/ros_ws/install/ugv_sdk')])}
[0.000387] (rslidar_msg) JobStarted: {'identifier': 'rslidar_msg'}
[0.002864] (scout_msgs) JobStarted: {'identifier': 'scout_msgs'}
[0.004415] (ugv_sdk) JobStarted: {'identifier': 'ugv_sdk'}
[0.005920] (ardupilot_mavros_bridge) JobStarted: {'identifier': 'ardupilot_mavros_bridge'}
[0.008745] (scout_description) JobStarted: {'identifier': 'scout_description'}
[0.010862] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'cmake'}
[0.011256] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/rslidar_msg', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.012908] (scout_msgs) JobProgress: {'identifier': 'scout_msgs', 'progress': 'cmake'}
[0.013297] (scout_msgs) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_msgs'], 'cwd': '/home/<USER>/ros_ws/build/scout_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/scout_msgs'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.014234] (ugv_sdk) JobProgress: {'identifier': 'ugv_sdk', 'progress': 'cmake'}
[0.014554] (ugv_sdk) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/ugv_sdk', '-DCATKIN_INSTALL_INTO_PREFIX_ROOT=0', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/ugv_sdk'], 'cwd': '/home/<USER>/ros_ws/build/ugv_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/ugv_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1')]), 'shell': False}
[0.099483] (-) TimerEvent: {}
[0.146501] (scout_msgs) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.146990] (scout_msgs) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.147036] (scout_msgs) StdoutLine: {'line': b'-- Found builtin_interfaces: 1.2.1 (/opt/ros/humble/share/builtin_interfaces/cmake)\n'}
[0.147069] (scout_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.147096] (scout_msgs) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.147122] (scout_msgs) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.147147] (scout_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.147172] (scout_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.147196] (scout_msgs) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.147221] (scout_msgs) StdoutLine: {'line': b'-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)\n'}
[0.147246] (ugv_sdk) StdoutLine: {'line': b'-- Build package with cmake\n'}
[0.147446] (ugv_sdk) StdoutLine: {'line': b'-- Tests will not be built\n'}
[0.147475] (ugv_sdk) StdoutLine: {'line': b"-- Project will be installed to /home/<USER>/ros_ws/install/ugv_sdk with 'make install'\n"}
[0.147504] (ugv_sdk) StdoutLine: {'line': b'--  - To install LIB components to /home/<USER>/ros_ws/install/ugv_sdk/lib\n'}
[0.147531] (ugv_sdk) StdoutLine: {'line': b'--  - To install BIN components to /home/<USER>/ros_ws/install/ugv_sdk/bin\n'}
[0.147557] (ugv_sdk) StdoutLine: {'line': b'--  - To install INCLUDE components to /home/<USER>/ros_ws/install/ugv_sdk/include\n'}
[0.147582] (ugv_sdk) StdoutLine: {'line': b'--  - To install CMAKE components to /home/<USER>/ros_ws/install/ugv_sdk/share/cmake/ugv_sdk\n'}
[0.147607] (ugv_sdk) StdoutLine: {'line': b'-- Configuring done\n'}
[0.147632] (ugv_sdk) StdoutLine: {'line': b'-- Generating done\n'}
[0.147657] (ugv_sdk) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/ugv_sdk\n'}
[0.147874] (rslidar_msg) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.148037] (rslidar_msg) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.148085] (rslidar_msg) StdoutLine: {'line': b'-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)\n'}
[0.148119] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.148151] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.148182] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.148213] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.148243] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.148325] (scout_description) JobProgress: {'identifier': 'scout_description', 'progress': 'cmake'}
[0.148740] (scout_description) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/scout_ros2/scout_description', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_description'], 'cwd': '/home/<USER>/ros_ws/build/scout_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/scout_description'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.150650] (ugv_sdk) CommandEnded: {'returncode': 0}
[0.150972] (ugv_sdk) JobProgress: {'identifier': 'ugv_sdk', 'progress': 'build'}
[0.151571] (ugv_sdk) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/ugv_sdk', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/ugv_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/ugv_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1')]), 'shell': False}
[0.153173] (rslidar_msg) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.160776] (scout_description) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.167992] (rslidar_msg) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.168792] (rslidar_msg) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.190856] (ugv_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target ugv_sdk\x1b[0m\n'}
[0.199615] (-) TimerEvent: {}
[0.218193] (ugv_sdk) StdoutLine: {'line': b'[ 42%] Built target ugv_sdk\n'}
[0.226391] (ugv_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target demo_robot_version\x1b[0m\n'}
[0.226539] (ugv_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target demo_protocol_detector\x1b[0m\n'}
[0.226590] (ugv_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target demo_tracer_robot\x1b[0m\n'}
[0.226680] (ugv_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target demo_scout_robot\x1b[0m\n'}
[0.228131] (ugv_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target demo_bunker_robot\x1b[0m\n'}
[0.228245] (ugv_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target demo_scout_mini_omni_robot\x1b[0m\n'}
[0.228652] (ugv_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target demo_ranger_robot\x1b[0m\n'}
[0.228979] (ugv_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target demo_hunter_robot\x1b[0m\n'}
[0.239088] (ugv_sdk) StdoutLine: {'line': b'[ 64%] Built target demo_bunker_robot\n'}
[0.239338] (ugv_sdk) StdoutLine: {'line': b'[ 64%] Built target demo_tracer_robot\n'}
[0.239412] (ugv_sdk) StdoutLine: {'line': b'[ 64%] Built target demo_protocol_detector\n'}
[0.241622] (rslidar_msg) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.243221] (ugv_sdk) StdoutLine: {'line': b'[ 71%] Built target demo_robot_version\n'}
[0.243983] (ugv_sdk) StdoutLine: {'line': b'[ 78%] Built target demo_scout_robot\n'}
[0.245293] (ugv_sdk) StdoutLine: {'line': b'[ 92%] Built target demo_hunter_robot\n'}
[0.245482] (ugv_sdk) StdoutLine: {'line': b'[ 92%] Built target demo_scout_mini_omni_robot\n'}
[0.246739] (ugv_sdk) StdoutLine: {'line': b'[100%] Built target demo_ranger_robot\n'}
[0.257690] (scout_description) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.257858] (ugv_sdk) CommandEnded: {'returncode': 0}
[0.258523] (ugv_sdk) JobProgress: {'identifier': 'ugv_sdk', 'progress': 'install'}
[0.264018] (ugv_sdk) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/ugv_sdk'], 'cwd': '/home/<USER>/ros_ws/build/ugv_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/ugv_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1')]), 'shell': False}
[0.268533] (ugv_sdk) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.268724] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/libugv_sdk.a\n'}
[0.268896] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk\n'}
[0.269006] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details\n'}
[0.269107] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface\n'}
[0.269181] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/robot_common_interface.hpp\n'}
[0.269251] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/agilex_message.h\n'}
[0.269320] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/scout_interface.hpp\n'}
[0.269391] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/agilex_types.h\n'}
[0.269459] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/ranger_interface.hpp\n'}
[0.269527] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/tracer_interface.hpp\n'}
[0.269592] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/titan_interface.hpp\n'}
[0.269668] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/bunker_interface.hpp\n'}
[0.269735] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/interface/hunter_interface.hpp\n'}
[0.269801] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port\n'}
[0.269868] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port/async_serial.hpp\n'}
[0.269934] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port/async_can.hpp\n'}
[0.269999] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/async_port/ring_buffer.hpp\n'}
[0.270089] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v2\n'}
[0.270200] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v2/protocol_v2_parser.hpp\n'}
[0.270402] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base\n'}
[0.270516] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/bunker_base.hpp\n'}
[0.270849] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/titan_base.hpp\n'}
[0.271075] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/agilex_base.hpp\n'}
[0.271506] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/hunter_base.hpp\n'}
[0.271744] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/ranger_base.hpp\n'}
[0.271934] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/tracer_base.hpp\n'}
[0.272119] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/robot_base/scout_base.hpp\n'}
[0.272281] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1\n'}
[0.272491] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1/protocol_v1_parser.hpp\n'}
[0.272876] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1/robot_limits.hpp\n'}
[0.273092] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/protocol_v1/agilex_msg_parser_v1.h\n'}
[0.273329] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/details/parser_base.hpp\n'}
[0.273412] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/utilities\n'}
[0.273665] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/utilities/protocol_detector.hpp\n'}
[0.273759] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot\n'}
[0.274249] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/titan_robot.hpp\n'}
[0.274327] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/ranger_robot.hpp\n'}
[0.274394] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/tracer_robot.hpp\n'}
[0.274458] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/hunter_robot.hpp\n'}
[0.274526] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/bunker_robot.hpp\n'}
[0.274592] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/include/ugv_sdk/mobile_robot/scout_robot.hpp\n'}
[0.274655] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkTargets.cmake\n'}
[0.274721] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkTargets-release.cmake\n'}
[0.274787] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkConfig.cmake\n'}
[0.274852] (ugv_sdk) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/ugv_sdk/lib/cmake/ugv_sdk/ugv_sdkConfigVersion.cmake\n'}
[0.274919] (ugv_sdk) CommandEnded: {'returncode': 0}
[0.282039] (rslidar_msg) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[0.285230] (ugv_sdk) JobEnded: {'identifier': 'ugv_sdk', 'rc': 0}
[0.299715] (-) TimerEvent: {}
[0.305073] (scout_description) StdoutLine: {'line': b'-- Configuring done\n'}
[0.306215] (scout_description) StdoutLine: {'line': b'-- Generating done\n'}
[0.307004] (scout_description) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/scout_description\n'}
[0.308868] (scout_description) CommandEnded: {'returncode': 0}
[0.309355] (scout_description) JobProgress: {'identifier': 'scout_description', 'progress': 'build'}
[0.309707] (scout_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/scout_description', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/scout_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/scout_description'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.343986] (scout_description) CommandEnded: {'returncode': 0}
[0.344685] (scout_description) JobProgress: {'identifier': 'scout_description', 'progress': 'install'}
[0.345590] (scout_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/scout_description'], 'cwd': '/home/<USER>/ros_ws/build/scout_description', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/scout_description'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.350943] (ardupilot_mavros_bridge) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'develop', '--uninstall', '--editable', '--build-directory', '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build'], 'cwd': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:en_US:en', 'USER': 'arduonboard', 'SSH_CLIENT': '************* 53444 22', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh', 'MOTD_SHOWN': 'pam', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '0', 'OLDPWD': '/home/<USER>/ros_ws/src', 'TERM_PROGRAM_VERSION': '1.101.2', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock', 'ROS_PYTHON_VERSION': '3', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/certs/ca-certificates.crt', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', '_CE_M': '', 'ROS_DISTRO': 'humble', 'LOGNAME': 'arduonboard', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '412', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'PATH': '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'a4', 'LC_ADDRESS': 'zh_CN.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'MAMBA_ROOT_PREFIX': '/home/<USER>/miniforge3', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt', 'LANG': 'zh_CN.UTF-8', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-52fd21289e.sock', 'TERM_PROGRAM': 'vscode', 'AMENT_PREFIX_PATH': '/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/miniforge3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'MAMBA_EXE': '/home/<USER>/miniforge3/bin/mamba', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'PWD': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'CONDA_EXE': '/home/<USER>/miniforge3/bin/conda', 'SSH_CONNECTION': '************* 53444 ************* 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1'}, 'shell': False}
[0.352282] (scout_msgs) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[0.352795] (scout_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.353291] (scout_description) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.353502] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//launch/scout_base_description.launch.py\n'}
[0.353635] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/generate_urdf.sh\n'}
[0.353733] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/scout_v2.urdf\n'}
[0.353827] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/scout_v2.xacro\n'}
[0.353935] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/scout_wheel_type1.xacro\n'}
[0.354010] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/scout_wheel_type2.xacro\n'}
[0.354113] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/base_link.dae\n'}
[0.354185] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/base_link_full.dae\n'}
[0.354254] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/hokuyo.dae\n'}
[0.354322] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/wheel_type1.dae\n'}
[0.354389] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/wheel_type2.dae\n'}
[0.354476] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/ament_index/resource_index/package_run_dependencies/scout_description\n'}
[0.354589] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/ament_index/resource_index/parent_prefix_path/scout_description\n'}
[0.354662] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/environment/ament_prefix_path.sh\n'}
[0.354737] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/environment/ament_prefix_path.dsv\n'}
[0.354804] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/environment/path.sh\n'}
[0.354872] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/environment/path.dsv\n'}
[0.354959] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/local_setup.bash\n'}
[0.355022] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/local_setup.sh\n'}
[0.355108] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/local_setup.zsh\n'}
[0.355174] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/local_setup.dsv\n'}
[0.355243] (scout_description) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros_ws/install/scout_description/share/scout_description/package.dsv\n'}
[0.362990] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/ament_index/resource_index/packages/scout_description\n'}
[0.363224] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/cmake/scout_descriptionConfig.cmake\n'}
[0.363318] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/cmake/scout_descriptionConfig-version.cmake\n'}
[0.363383] (scout_description) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/package.xml\n'}
[0.364374] (scout_description) CommandEnded: {'returncode': 0}
[0.372741] (scout_description) JobEnded: {'identifier': 'scout_description', 'rc': 0}
[0.399909] (-) TimerEvent: {}
[0.473191] (rslidar_msg) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[0.482961] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running develop\n'}
[0.500042] (-) TimerEvent: {}
[0.551958] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Removing /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot-mavros-bridge.egg-link (link to .)\n'}
[0.572759] (ardupilot_mavros_bridge) CommandEnded: {'returncode': 0}
[0.573766] (ardupilot_mavros_bridge) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/ardupilot_mavros_bridge', 'build', '--build-base', '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build', 'install', '--record', '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log', '--single-version-externally-managed', 'install_data', '--force'], 'cwd': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge', 'env': {'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:en_US:en', 'USER': 'arduonboard', 'SSH_CLIENT': '************* 53444 22', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'tty', 'GIT_ASKPASS': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib', 'BROWSER': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh', 'MOTD_SHOWN': 'pam', 'HOME': '/home/<USER>', 'CONDA_SHLVL': '0', 'OLDPWD': '/home/<USER>/ros_ws/src', 'TERM_PROGRAM_VERSION': '1.101.2', 'VSCODE_IPC_HOOK_CLI': '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock', 'ROS_PYTHON_VERSION': '3', 'VSCODE_GIT_ASKPASS_MAIN': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node', 'SSL_CERT_FILE': '/usr/lib/ssl/certs/ca-certificates.crt', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', '_CE_M': '', 'ROS_DISTRO': 'humble', 'LOGNAME': 'arduonboard', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'XDG_SESSION_CLASS': 'user', 'TERM': 'xterm-256color', 'XDG_SESSION_ID': '412', '_CE_CONDA': '', 'ROS_LOCALHOST_ONLY': '0', 'PATH': '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand', 'PAPERSIZE': 'a4', 'LC_ADDRESS': 'zh_CN.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'MAMBA_ROOT_PREFIX': '/home/<USER>/miniforge3', 'SSL_CERT_DIR': '/usr/lib/ssl/certs', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt', 'LANG': 'zh_CN.UTF-8', 'LC_TELEPHONE': 'zh_CN.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-52fd21289e.sock', 'TERM_PROGRAM': 'vscode', 'AMENT_PREFIX_PATH': '/opt/ros/humble', 'CONDA_PYTHON_EXE': '/home/<USER>/miniforge3/bin/python', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'MAMBA_EXE': '/home/<USER>/miniforge3/bin/mamba', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'PWD': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'CONDA_EXE': '/home/<USER>/miniforge3/bin/conda', 'SSH_CONNECTION': '************* 53444 ************* 22', 'XDG_DATA_DIRS': '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1'}, 'shell': False}
[0.600121] (-) TimerEvent: {}
[0.615040] (scout_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.697806] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running egg_info\n'}
[0.698194] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/PKG-INFO\n'}
[0.698354] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing dependency_links to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/dependency_links.txt\n'}
[0.698523] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing entry points to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/entry_points.txt\n'}
[0.698623] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing requirements to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/requires.txt\n'}
[0.698690] (ardupilot_mavros_bridge) StdoutLine: {'line': b'writing top-level names to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/top_level.txt\n'}
[0.699517] (ardupilot_mavros_bridge) StdoutLine: {'line': b"reading manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'\n"}
[0.700121] (ardupilot_mavros_bridge) StdoutLine: {'line': b"writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'\n"}
[0.700218] (-) TimerEvent: {}
[0.700361] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running build\n'}
[0.700452] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running build_py\n'}
[0.700536] (ardupilot_mavros_bridge) StdoutLine: {'line': b'creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build\n'}
[0.700606] (ardupilot_mavros_bridge) StdoutLine: {'line': b'creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib\n'}
[0.700681] (ardupilot_mavros_bridge) StdoutLine: {'line': b'creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge\n'}
[0.700744] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge\n'}
[0.700809] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying ardupilot_mavros_bridge/mission_control_node.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge\n'}
[0.700884] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge\n'}
[0.700946] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install\n'}
[0.701006] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_lib\n'}
[0.701184] (ardupilot_mavros_bridge) StdoutLine: {'line': b'creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge\n'}
[0.701274] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge\n'}
[0.701360] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/mission_control_node.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge\n'}
[0.701432] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge\n'}
[0.701584] (ardupilot_mavros_bridge) StdoutLine: {'line': b'byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/__init__.py to __init__.cpython-310.pyc\n'}
[0.701696] (ardupilot_mavros_bridge) StdoutLine: {'line': b'byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/mission_control_node.py to mission_control_node.cpython-310.pyc\n'}
[0.703320] (ardupilot_mavros_bridge) StdoutLine: {'line': b'byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/mavros_bridge_node.py to mavros_bridge_node.cpython-310.pyc\n'}
[0.703980] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_data\n'}
[0.704110] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying resource/ardupilot_mavros_bridge -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ament_index/resource_index/packages\n'}
[0.704246] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying package.xml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge\n'}
[0.704314] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying launch/apm.launch -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch\n'}
[0.704380] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying launch/apm_optimized.launch -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch\n'}
[0.704497] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying launch/mavros_bridge.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch\n'}
[0.704564] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying launch/complete_system.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch\n'}
[0.704629] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying launch/navigation_system.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch\n'}
[0.704693] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying config/apm_config.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config\n'}
[0.704758] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying config/apm_config_optimized.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config\n'}
[0.704843] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying config/apm_pluginlists.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config\n'}
[0.704905] (ardupilot_mavros_bridge) StdoutLine: {'line': b'copying config/nav2_params.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config\n'}
[0.704966] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_egg_info\n'}
[0.705634] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Copying ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info\n'}
[0.705969] (ardupilot_mavros_bridge) StdoutLine: {'line': b'running install_scripts\n'}
[0.715292] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.715450] (scout_msgs) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.718318] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge\n'}
[0.718464] (ardupilot_mavros_bridge) StdoutLine: {'line': b'Installing mission_control_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge\n'}
[0.718633] (ardupilot_mavros_bridge) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log'\n"}
[0.734874] (ardupilot_mavros_bridge) CommandEnded: {'returncode': 0}
[0.737147] (scout_msgs) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[0.741669] (ardupilot_mavros_bridge) JobEnded: {'identifier': 'ardupilot_mavros_bridge', 'rc': 0}
[0.751545] (scout_msgs) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[0.751716] (scout_msgs) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[0.751793] (scout_msgs) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so\n'}
[0.766358] (scout_msgs) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.771212] (scout_msgs) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[0.771916] (scout_msgs) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.772295] (scout_msgs) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.773619] (scout_msgs) StdoutLine: {'line': b'-- Configuring done\n'}
[0.793162] (scout_msgs) StdoutLine: {'line': b'-- Generating done\n'}
[0.799967] (scout_msgs) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/scout_msgs\n'}
[0.800291] (-) TimerEvent: {}
[0.805555] (scout_msgs) CommandEnded: {'returncode': 0}
[0.806010] (scout_msgs) JobProgress: {'identifier': 'scout_msgs', 'progress': 'build'}
[0.806274] (scout_msgs) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/scout_msgs', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/scout_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/scout_msgs'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.823396] (rslidar_msg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.839614] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_generator_c\x1b[0m\n'}
[0.845413] (scout_msgs) StdoutLine: {'line': b'[  1%] Built target scout_msgs__cpp\n'}
[0.846627] (rslidar_msg) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[0.850309] (scout_msgs) StdoutLine: {'line': b'[ 12%] Built target scout_msgs__rosidl_generator_c\n'}
[0.852411] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_typesupport_introspection_cpp\x1b[0m\n'}
[0.852737] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_typesupport_fastrtps_cpp\x1b[0m\n'}
[0.853004] (scout_msgs) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_symlink_scout_msgs\n'}
[0.855639] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_typesupport_cpp\x1b[0m\n'}
[0.858133] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_typesupport_fastrtps_c\x1b[0m\n'}
[0.860800] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_typesupport_c\x1b[0m\n'}
[0.861072] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_typesupport_introspection_c\x1b[0m\n'}
[0.861434] (scout_msgs) StdoutLine: {'line': b'[ 23%] Built target scout_msgs__rosidl_typesupport_fastrtps_cpp\n'}
[0.863459] (scout_msgs) StdoutLine: {'line': b'[ 34%] Built target scout_msgs__rosidl_typesupport_introspection_cpp\n'}
[0.867842] (scout_msgs) StdoutLine: {'line': b'[ 46%] Built target scout_msgs__rosidl_typesupport_cpp\n'}
[0.869372] (scout_msgs) StdoutLine: {'line': b'[ 57%] Built target scout_msgs__rosidl_typesupport_c\n'}
[0.870085] (scout_msgs) StdoutLine: {'line': b'[ 68%] Built target scout_msgs__rosidl_typesupport_fastrtps_c\n'}
[0.871385] (scout_msgs) StdoutLine: {'line': b'[ 79%] Built target scout_msgs__rosidl_typesupport_introspection_c\n'}
[0.871619] (rslidar_msg) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[0.871706] (rslidar_msg) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[0.871774] (rslidar_msg) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so\n'}
[0.886904] (scout_msgs) StdoutLine: {'line': b'[ 79%] Built target scout_msgs\n'}
[0.889772] (rslidar_msg) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.895132] (rslidar_msg) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[0.897775] (rslidar_msg) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.899146] (rslidar_msg) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.900359] (-) TimerEvent: {}
[0.900508] (rslidar_msg) StdoutLine: {'line': b'-- Configuring done\n'}
[0.904239] (scout_msgs) StdoutLine: {'line': b'[ 80%] Built target scout_msgs__py\n'}
[0.911200] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_generator_py\x1b[0m\n'}
[0.916850] (rslidar_msg) StdoutLine: {'line': b'-- Generating done\n'}
[0.923490] (rslidar_msg) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_msg\n'}
[0.923771] (scout_msgs) StdoutLine: {'line': b'[ 90%] Built target scout_msgs__rosidl_generator_py\n'}
[0.930003] (rslidar_msg) CommandEnded: {'returncode': 0}
[0.930620] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'build'}
[0.930885] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/rslidar_msg', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.932397] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_typesupport_fastrtps_c__pyext\x1b[0m\n'}
[0.932584] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_typesupport_c__pyext\x1b[0m\n'}
[0.933581] (scout_msgs) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_msgs__rosidl_typesupport_introspection_c__pyext\x1b[0m\n'}
[0.939848] (scout_msgs) StdoutLine: {'line': b'[ 93%] Built target scout_msgs__rosidl_typesupport_fastrtps_c__pyext\n'}
[0.942265] (scout_msgs) StdoutLine: {'line': b'[ 96%] Built target scout_msgs__rosidl_typesupport_c__pyext\n'}
[0.945786] (scout_msgs) StdoutLine: {'line': b'[100%] Built target scout_msgs__rosidl_typesupport_introspection_c__pyext\n'}
[0.969900] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_c\x1b[0m\n'}
[0.976247] (rslidar_msg) StdoutLine: {'line': b'[  3%] Built target rslidar_msg__cpp\n'}
[0.980575] (rslidar_msg) StdoutLine: {'line': b'[ 12%] Built target rslidar_msg__rosidl_generator_c\n'}
[0.980817] (scout_msgs) StdoutLine: {'line': b'running egg_info\n'}
[0.981333] (scout_msgs) StdoutLine: {'line': b'writing scout_msgs.egg-info/PKG-INFO\n'}
[0.981649] (scout_msgs) StdoutLine: {'line': b'writing dependency_links to scout_msgs.egg-info/dependency_links.txt\n'}
[0.981789] (scout_msgs) StdoutLine: {'line': b'writing top-level names to scout_msgs.egg-info/top_level.txt\n'}
[0.983128] (rslidar_msg) StdoutLine: {'line': b'[ 12%] Built target ament_cmake_python_symlink_rslidar_msg\n'}
[0.983430] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_cpp\x1b[0m\n'}
[0.983540] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_cpp\x1b[0m\n'}
[0.983712] (scout_msgs) StdoutLine: {'line': b"reading manifest file 'scout_msgs.egg-info/SOURCES.txt'\n"}
[0.984132] (scout_msgs) StdoutLine: {'line': b"writing manifest file 'scout_msgs.egg-info/SOURCES.txt'\n"}
[0.987323] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_cpp\x1b[0m\n'}
[0.992281] (rslidar_msg) StdoutLine: {'line': b'[ 22%] Built target rslidar_msg__rosidl_typesupport_fastrtps_cpp\n'}
[0.992556] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c\x1b[0m\n'}
[0.992624] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c\x1b[0m\n'}
[0.992772] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c\x1b[0m\n'}
[0.997594] (rslidar_msg) StdoutLine: {'line': b'[ 32%] Built target rslidar_msg__rosidl_typesupport_introspection_cpp\n'}
[0.998515] (rslidar_msg) StdoutLine: {'line': b'[ 41%] Built target rslidar_msg__rosidl_typesupport_cpp\n'}
[1.000474] (-) TimerEvent: {}
[1.003102] (rslidar_msg) StdoutLine: {'line': b'[ 51%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c\n'}
[1.004269] (rslidar_msg) StdoutLine: {'line': b'[ 70%] Built target rslidar_msg__rosidl_typesupport_c\n'}
[1.004372] (rslidar_msg) StdoutLine: {'line': b'[ 70%] Built target rslidar_msg__rosidl_typesupport_introspection_c\n'}
[1.020216] (rslidar_msg) StdoutLine: {'line': b'[ 70%] Built target rslidar_msg\n'}
[1.020459] (scout_msgs) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_scout_msgs_egg\n'}
[1.031195] (scout_msgs) CommandEnded: {'returncode': 0}
[1.031894] (scout_msgs) JobProgress: {'identifier': 'scout_msgs', 'progress': 'install'}
[1.032298] (scout_msgs) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/scout_msgs'], 'cwd': '/home/<USER>/ros_ws/build/scout_msgs', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/scout_msgs'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[1.035250] (rslidar_msg) StdoutLine: {'line': b'[ 74%] Built target rslidar_msg__py\n'}
[1.038177] (scout_msgs) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.038546] (scout_msgs) StdoutLine: {'line': b'-- Execute custom install script\n'}
[1.038668] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/ament_index/resource_index/rosidl_interfaces/scout_msgs\n'}
[1.038939] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__functions.h\n'}
[1.039026] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__struct.h\n'}
[1.039148] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__type_support.h\n'}
[1.039223] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__functions.h\n'}
[1.039295] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__struct.h\n'}
[1.039384] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__type_support.h\n'}
[1.039502] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__functions.h\n'}
[1.039574] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__struct.h\n'}
[1.039643] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__type_support.h\n'}
[1.039713] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__functions.h\n'}
[1.039812] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__struct.h\n'}
[1.039881] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__type_support.h\n'}
[1.039949] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__functions.h\n'}
[1.040017] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__struct.h\n'}
[1.040157] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__type_support.h\n'}
[1.040257] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/rosidl_generator_c__visibility_control.h\n'}
[1.040342] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_actuator_state.h\n'}
[1.040413] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_light_cmd.h\n'}
[1.040482] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_light_state.h\n'}
[1.040571] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_rc_state.h\n'}
[1.040642] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_status.h\n'}
[1.040710] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/environment/library_path.sh\n'}
[1.040779] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/environment/library_path.dsv\n'}
[1.040848] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__rosidl_typesupport_fastrtps_c.h\n'}
[1.040916] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__rosidl_typesupport_fastrtps_c.h\n'}
[1.040984] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__rosidl_typesupport_fastrtps_c.h\n'}
[1.041112] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__rosidl_typesupport_fastrtps_c.h\n'}
[1.041205] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__rosidl_typesupport_fastrtps_c.h\n'}
[1.041298] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[1.041369] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__builder.hpp\n'}
[1.041440] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__struct.hpp\n'}
[1.041557] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__traits.hpp\n'}
[1.041627] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__type_support.hpp\n'}
[1.041714] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__builder.hpp\n'}
[1.041781] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__struct.hpp\n'}
[1.041847] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__traits.hpp\n'}
[1.041916] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__type_support.hpp\n'}
[1.042010] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__builder.hpp\n'}
[1.042114] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__struct.hpp\n'}
[1.042180] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__traits.hpp\n'}
[1.042246] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__type_support.hpp\n'}
[1.042314] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__builder.hpp\n'}
[1.042383] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__struct.hpp\n'}
[1.042492] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__traits.hpp\n'}
[1.042559] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__type_support.hpp\n'}
[1.042637] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__builder.hpp\n'}
[1.042708] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__struct.hpp\n'}
[1.042777] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__traits.hpp\n'}
[1.042847] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__type_support.hpp\n'}
[1.042949] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[1.043015] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_actuator_state.hpp\n'}
[1.043118] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_light_cmd.hpp\n'}
[1.043211] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_light_state.hpp\n'}
[1.043275] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_rc_state.hpp\n'}
[1.043338] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/scout_status.hpp\n'}
[1.043402] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[1.043469] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[1.043537] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[1.043633] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[1.043720] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[1.043787] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[1.043854] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__rosidl_typesupport_introspection_c.h\n'}
[1.043921] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__rosidl_typesupport_introspection_c.h\n'}
[1.043987] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__rosidl_typesupport_introspection_c.h\n'}
[1.044102] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__rosidl_typesupport_introspection_c.h\n'}
[1.044194] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__rosidl_typesupport_introspection_c.h\n'}
[1.044261] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[1.044350] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_actuator_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[1.044416] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_cmd__rosidl_typesupport_introspection_cpp.hpp\n'}
[1.044496] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_light_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[1.044606] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_rc_state__rosidl_typesupport_introspection_cpp.hpp\n'}
[1.044684] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/include/scout_msgs/scout_msgs/msg/detail/scout_status__rosidl_typesupport_introspection_cpp.hpp\n'}
[1.044751] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/environment/pythonpath.sh\n'}
[1.044818] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/environment/pythonpath.dsv\n'}
[1.044885] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs-0.1.0-py3.10.egg-info/PKG-INFO\n'}
[1.044951] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs-0.1.0-py3.10.egg-info/SOURCES.txt\n'}
[1.045023] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs-0.1.0-py3.10.egg-info/dependency_links.txt\n'}
[1.045156] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs-0.1.0-py3.10.egg-info/top_level.txt\n'}
[1.045223] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/__init__.py\n'}
[1.045318] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/_scout_msgs_s.ep.rosidl_typesupport_c.c\n'}
[1.045384] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/_scout_msgs_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[1.045449] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/_scout_msgs_s.ep.rosidl_typesupport_introspection_c.c\n'}
[1.045538] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/libscout_msgs__rosidl_generator_py.so\n'}
[1.045605] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/__init__.py\n'}
[1.045671] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_actuator_state.py\n'}
[1.045772] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_actuator_state_s.c\n'}
[1.045839] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_light_cmd.py\n'}
[1.045903] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_light_cmd_s.c\n'}
[1.045967] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_light_state.py\n'}
[1.046035] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_light_state_s.c\n'}
[1.046152] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_rc_state.py\n'}
[1.046196] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_rc_state_s.c\n'}
[1.046223] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_status.py\n'}
[1.046248] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg/_scout_status_s.c\n'}
[1.046272] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/scout_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.046296] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/scout_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.046323] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/scout_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.046348] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/scout_msgs_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.046371] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/scout_msgs_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.046397] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_generator_py\x1b[0m\n'}
[1.046426] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/scout_msgs_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.046449] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutActuatorState.idl\n'}
[1.046471] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutLightCmd.idl\n'}
[1.046496] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutLightState.idl\n'}
[1.046521] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutRCState.idl\n'}
[1.046545] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutStatus.idl\n'}
[1.046569] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutActuatorState.msg\n'}
[1.046591] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutLightCmd.msg\n'}
[1.046614] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutLightState.msg\n'}
[1.046637] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutRCState.msg\n'}
[1.046658] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/msg/ScoutStatus.msg\n'}
[1.046680] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/ament_index/resource_index/package_run_dependencies/scout_msgs\n'}
[1.046703] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/ament_index/resource_index/parent_prefix_path/scout_msgs\n'}
[1.046726] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/environment/ament_prefix_path.sh\n'}
[1.046749] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/environment/ament_prefix_path.dsv\n'}
[1.046773] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/environment/path.sh\n'}
[1.046797] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/environment/path.dsv\n'}
[1.046820] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/local_setup.bash\n'}
[1.046842] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/local_setup.sh\n'}
[1.046864] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/local_setup.zsh\n'}
[1.046886] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/local_setup.dsv\n'}
[1.046909] (scout_msgs) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.dsv\n'}
[1.053824] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/ament_index/resource_index/packages/scout_msgs\n'}
[1.054073] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/rosidl_cmake-extras.cmake\n'}
[1.054196] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[1.054288] (rslidar_msg) StdoutLine: {'line': b'[ 80%] Built target rslidar_msg__rosidl_generator_py\n'}
[1.054402] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[1.054479] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[1.054564] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/ament_cmake_export_targets-extras.cmake\n'}
[1.054648] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[1.054751] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[1.054822] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgsConfig.cmake\n'}
[1.054890] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgsConfig-version.cmake\n'}
[1.054958] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.xml\n'}
[1.055025] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/lib/libscout_msgs__rosidl_generator_c.so\n'}
[1.055147] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/lib/libscout_msgs__rosidl_typesupport_fastrtps_c.so\n'}
[1.055216] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/lib/libscout_msgs__rosidl_typesupport_fastrtps_cpp.so\n'}
[1.055284] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/lib/libscout_msgs__rosidl_typesupport_introspection_c.so\n'}
[1.055351] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/lib/libscout_msgs__rosidl_typesupport_c.so\n'}
[1.055418] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/lib/libscout_msgs__rosidl_typesupport_introspection_cpp.so\n'}
[1.055483] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/lib/libscout_msgs__rosidl_typesupport_cpp.so\n'}
[1.062853] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_introspection_c__pyext\x1b[0m\n'}
[1.063065] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext\x1b[0m\n'}
[1.063147] (rslidar_msg) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_msg__rosidl_typesupport_c__pyext\x1b[0m\n'}
[1.073820] (rslidar_msg) StdoutLine: {'line': b'[ 87%] Built target rslidar_msg__rosidl_typesupport_introspection_c__pyext\n'}
[1.074054] (rslidar_msg) StdoutLine: {'line': b'[ 93%] Built target rslidar_msg__rosidl_typesupport_fastrtps_c__pyext\n'}
[1.074174] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target rslidar_msg__rosidl_typesupport_c__pyext\n'}
[1.074407] (scout_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs'...\n"}
[1.074499] (scout_msgs) StdoutLine: {'line': b"Compiling '/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/__init__.py'...\n"}
[1.074587] (scout_msgs) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages/scout_msgs/msg'...\n"}
[1.077496] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/lib/libscout_msgs__rosidl_generator_py.so\n'}
[1.077628] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/export_scout_msgs__rosidl_generator_cExport.cmake\n'}
[1.077701] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/export_scout_msgs__rosidl_generator_cExport-noconfig.cmake\n'}
[1.077790] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/export_scout_msgs__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[1.077917] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/export_scout_msgs__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[1.077998] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/export_scout_msgs__rosidl_generator_cppExport.cmake\n'}
[1.078087] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/export_scout_msgs__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[1.078155] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/export_scout_msgs__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[1.078230] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgs__rosidl_typesupport_introspection_cExport.cmake\n'}
[1.078294] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgs__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[1.078358] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgs__rosidl_typesupport_cExport.cmake\n'}
[1.078420] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgs__rosidl_typesupport_cExport-noconfig.cmake\n'}
[1.078482] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgs__rosidl_typesupport_introspection_cppExport.cmake\n'}
[1.078543] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgs__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[1.078605] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgs__rosidl_typesupport_cppExport.cmake\n'}
[1.078670] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/scout_msgs__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[1.078732] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/export_scout_msgs__rosidl_generator_pyExport.cmake\n'}
[1.078794] (scout_msgs) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake/export_scout_msgs__rosidl_generator_pyExport-noconfig.cmake\n'}
[1.079458] (scout_msgs) CommandEnded: {'returncode': 0}
[1.087716] (scout_msgs) JobEnded: {'identifier': 'scout_msgs', 'rc': 0}
[1.088297] (scout_base) JobStarted: {'identifier': 'scout_base'}
[1.093799] (scout_base) JobProgress: {'identifier': 'scout_base', 'progress': 'cmake'}
[1.094632] (scout_base) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/scout_ros2/scout_base', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_base'], 'cwd': '/home/<USER>/ros_ws/build/scout_base', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('ROS_PACKAGE_PATH', '/home/<USER>/ros_ws/install/ugv_sdk/share'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/scout_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('PKG_CONFIG_PATH', '/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/scout_msgs:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/scout_base'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs')]), 'shell': False}
[1.100553] (-) TimerEvent: {}
[1.103880] (scout_base) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[1.113006] (rslidar_msg) StdoutLine: {'line': b'running egg_info\n'}
[1.113409] (rslidar_msg) StdoutLine: {'line': b'writing rslidar_msg.egg-info/PKG-INFO\n'}
[1.113553] (rslidar_msg) StdoutLine: {'line': b'writing dependency_links to rslidar_msg.egg-info/dependency_links.txt\n'}
[1.113660] (rslidar_msg) StdoutLine: {'line': b'writing top-level names to rslidar_msg.egg-info/top_level.txt\n'}
[1.114811] (rslidar_msg) StdoutLine: {'line': b"reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'\n"}
[1.115105] (rslidar_msg) StdoutLine: {'line': b"writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'\n"}
[1.134079] (rslidar_msg) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_rslidar_msg_egg\n'}
[1.143558] (rslidar_msg) CommandEnded: {'returncode': 0}
[1.144977] (rslidar_msg) JobProgress: {'identifier': 'rslidar_msg', 'progress': 'install'}
[1.145203] (rslidar_msg) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/rslidar_msg'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_msg', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_msg'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[1.150604] (rslidar_msg) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.150842] (rslidar_msg) StdoutLine: {'line': b'-- Execute custom install script\n'}
[1.150956] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/rosidl_interfaces/rslidar_msg\n'}
[1.151157] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__functions.h\n'}
[1.151266] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.h\n'}
[1.151348] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.h\n'}
[1.151416] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_c__visibility_control.h\n'}
[1.151515] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.h\n'}
[1.151582] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.sh\n'}
[1.151657] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/library_path.dsv\n'}
[1.151723] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_c.h\n'}
[1.151810] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[1.151905] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__builder.hpp\n'}
[1.151974] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__struct.hpp\n'}
[1.152063] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__traits.hpp\n'}
[1.152153] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__type_support.hpp\n'}
[1.152216] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[1.152303] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rslidar_packet.hpp\n'}
[1.152367] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[1.152431] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[1.152524] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_c.h\n'}
[1.152589] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[1.152683] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/include/rslidar_msg/rslidar_msg/msg/detail/rslidar_packet__rosidl_typesupport_introspection_cpp.hpp\n'}
[1.152754] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.sh\n'}
[1.152839] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/pythonpath.dsv\n'}
[1.152926] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[1.153178] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[1.153283] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[1.153380] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg-0.0.0-py3.10.egg-info/top_level.txt\n'}
[1.153447] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py\n'}
[1.153514] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_c.c\n'}
[1.153580] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[1.153646] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/_rslidar_msg_s.ep.rosidl_typesupport_introspection_c.c\n'}
[1.153736] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/librslidar_msg__rosidl_generator_py.so\n'}
[1.153846] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/__init__.py\n'}
[1.153914] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet.py\n'}
[1.153980] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg/_rslidar_packet_s.c\n'}
[1.154079] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.154164] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.154231] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.154298] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.154365] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.154471] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/rslidar_msg_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[1.154540] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.idl\n'}
[1.154606] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/msg/RslidarPacket.msg\n'}
[1.154671] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/package_run_dependencies/rslidar_msg\n'}
[1.154736] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/parent_prefix_path/rslidar_msg\n'}
[1.154833] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.sh\n'}
[1.154896] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/ament_prefix_path.dsv\n'}
[1.154960] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.sh\n'}
[1.155022] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/environment/path.dsv\n'}
[1.155099] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.bash\n'}
[1.155164] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.sh\n'}
[1.155227] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.zsh\n'}
[1.155290] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/local_setup.dsv\n'}
[1.155357] (rslidar_msg) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv\n'}
[1.162610] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/ament_index/resource_index/packages/rslidar_msg\n'}
[1.162725] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake-extras.cmake\n'}
[1.162812] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[1.162879] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[1.162964] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[1.163072] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/ament_cmake_export_targets-extras.cmake\n'}
[1.163168] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[1.163231] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[1.163297] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig.cmake\n'}
[1.163363] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msgConfig-version.cmake\n'}
[1.163448] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.xml\n'}
[1.163511] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_c.so\n'}
[1.163577] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_c.so\n'}
[1.163670] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_fastrtps_cpp.so\n'}
[1.163739] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_c.so\n'}
[1.163832] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_c.so\n'}
[1.163908] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_introspection_cpp.so\n'}
[1.163974] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_typesupport_cpp.so\n'}
[1.177760] (scout_base) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[1.181417] (scout_base) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[1.182686] (rslidar_msg) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg'...\n"}
[1.182762] (rslidar_msg) StdoutLine: {'line': b"Compiling '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/__init__.py'...\n"}
[1.182826] (rslidar_msg) StdoutLine: {'line': b"Listing '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages/rslidar_msg/msg'...\n"}
[1.185688] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/lib/librslidar_msg__rosidl_generator_py.so\n'}
[1.185841] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport.cmake\n'}
[1.185929] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cExport-noconfig.cmake\n'}
[1.186016] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[1.186140] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cExport-noconfig.cmake\n'}
[1.186437] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_cppExport.cmake\n'}
[1.186481] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[1.186511] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_typesupport_fastrtps_cppExport-noconfig.cmake\n'}
[1.186538] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport.cmake\n'}
[1.186564] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cExport-noconfig.cmake\n'}
[1.186604] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport.cmake\n'}
[1.186630] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cExport-noconfig.cmake\n'}
[1.186656] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport.cmake\n'}
[1.186708] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_introspection_cppExport-noconfig.cmake\n'}
[1.186737] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport.cmake\n'}
[1.186761] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/rslidar_msg__rosidl_typesupport_cppExport-noconfig.cmake\n'}
[1.186805] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport.cmake\n'}
[1.186830] (rslidar_msg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake/export_rslidar_msg__rosidl_generator_pyExport-noconfig.cmake\n'}
[1.188703] (rslidar_msg) CommandEnded: {'returncode': 0}
[1.197684] (scout_base) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.199643] (scout_base) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.200616] (-) TimerEvent: {}
[1.203081] (scout_base) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[1.206487] (rslidar_msg) JobEnded: {'identifier': 'rslidar_msg', 'rc': 0}
[1.207815] (rslidar_sdk) JobStarted: {'identifier': 'rslidar_sdk'}
[1.210277] (scout_base) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.213533] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'cmake'}
[1.214359] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ros_ws/src/rslidar_sdk', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble')]), 'shell': False}
[1.222881] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.223063] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- POINT_TYPE is XYZI\x1b[0m\n'}
[1.223124] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.223216] (scout_base) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.228208] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.228399] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- ROS Not Found. ROS Support is turned Off.\x1b[0m\n'}
[1.228456] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.250731] (scout_base) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.251592] (scout_base) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.300750] (-) TimerEvent: {}
[1.311349] (rslidar_sdk) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[1.314209] (scout_base) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.336593] (rslidar_sdk) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.338163] (rslidar_sdk) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.342151] (rslidar_sdk) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[1.345717] (scout_base) StdoutLine: {'line': b'-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[1.348424] (rslidar_sdk) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.355697] (rslidar_sdk) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.357588] (scout_base) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.13 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[1.360105] (scout_base) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[1.360892] (scout_base) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[1.372777] (rslidar_sdk) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.373492] (rslidar_sdk) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.400843] (-) TimerEvent: {}
[1.405486] (scout_base) StdoutLine: {'line': b'-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[1.414096] (scout_base) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[1.423161] (scout_base) StdoutLine: {'line': b'-- Found scout_msgs: 0.1.0 (/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake)\n'}
[1.431900] (rslidar_sdk) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.433033] (scout_base) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.458681] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.458876] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- ROS2 Found. ROS2 Support is turned On.\x1b[0m\n'}
[1.458950] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.459017] (rslidar_sdk) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[1.473221] (scout_base) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[1.473721] (scout_base) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[1.473872] (scout_base) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/ros_ws/src/scout_ros2/scout_base/include;/home/<USER>/ros_ws/src/scout_ros2/scout_base/$<BUILD_INTERFACE:/opt/ros/humble/include/tf2_geometry_msgs\n'}
[1.473943] (scout_base) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[1.474241] (scout_base) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[1.474325] (scout_base) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[1.474431] (scout_base) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[1.474630] (scout_base) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.474793] (scout_base) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[1.474978] (rslidar_sdk) StdoutLine: {'line': b'-- Found rslidar_msg: 0.0.0 (/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake)\n'}
[1.475177] (scout_base) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[1.475260] (scout_base) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[1.475355] (scout_base) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.475824] (scout_base) StdoutLine: {'line': b'-- Configuring done\n'}
[1.482576] (scout_base) StdoutLine: {'line': b'-- Generating done\n'}
[1.483349] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.483437] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- CMake run for UNIX GNU Compiler\x1b[0m\n'}
[1.483499] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.483668] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.483734] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m-- rs_driver Version : v1.5.17\x1b[0m\n'}
[1.483794] (rslidar_sdk) StderrLine: {'line': b'\x1b[0m=============================================================\x1b[0m\n'}
[1.485092] (scout_base) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/scout_base\n'}
[1.490579] (scout_base) CommandEnded: {'returncode': 0}
[1.491055] (scout_base) JobProgress: {'identifier': 'scout_base', 'progress': 'build'}
[1.491294] (scout_base) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/scout_base', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/scout_base', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('ROS_PACKAGE_PATH', '/home/<USER>/ros_ws/install/ugv_sdk/share'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/scout_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('PKG_CONFIG_PATH', '/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/scout_msgs:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/scout_base'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs')]), 'shell': False}
[1.500936] (-) TimerEvent: {}
[1.526557] (rslidar_sdk) StdoutLine: {'line': b'-- Configuring done\n'}
[1.527197] (scout_base) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target scout_base_node\x1b[0m\n'}
[1.532094] (rslidar_sdk) StdoutLine: {'line': b'-- Generating done\n'}
[1.534140] (rslidar_sdk) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_sdk\n'}
[1.539288] (rslidar_sdk) CommandEnded: {'returncode': 0}
[1.539988] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'build'}
[1.540302] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ros_ws/build/rslidar_sdk', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble')]), 'shell': False}
[1.545601] (scout_base) StdoutLine: {'line': b'[100%] Built target scout_base_node\n'}
[1.553945] (scout_base) CommandEnded: {'returncode': 0}
[1.554514] (scout_base) JobProgress: {'identifier': 'scout_base', 'progress': 'install'}
[1.554755] (scout_base) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/scout_base'], 'cwd': '/home/<USER>/ros_ws/build/scout_base', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('ROS_PACKAGE_PATH', '/home/<USER>/ros_ws/install/ugv_sdk/share'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/scout_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('PKG_CONFIG_PATH', '/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/scout_msgs:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/scout_base'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs')]), 'shell': False}
[1.560490] (scout_base) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1.560710] (scout_base) StdoutLine: {'line': b'-- Execute custom install script\n'}
[1.560860] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/lib/scout_base/scout_base_node\n'}
[1.561024] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_base.launch.py\n'}
[1.561136] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_mini_base.launch.py\n'}
[1.561244] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_mini_omni_base.launch.py\n'}
[1.561318] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/package_run_dependencies/scout_base\n'}
[1.561409] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/parent_prefix_path/scout_base\n'}
[1.561515] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/ament_prefix_path.sh\n'}
[1.561586] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/ament_prefix_path.dsv\n'}
[1.561655] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/path.sh\n'}
[1.561724] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/path.dsv\n'}
[1.561820] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.bash\n'}
[1.561886] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.sh\n'}
[1.561959] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.zsh\n'}
[1.562023] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.dsv\n'}
[1.562091] (scout_base) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros_ws/install/scout_base/share/scout_base/package.dsv\n'}
[1.568584] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/packages/scout_base\n'}
[1.568793] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/cmake/scout_baseConfig.cmake\n'}
[1.568878] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/cmake/scout_baseConfig-version.cmake\n'}
[1.568981] (scout_base) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/package.xml\n'}
[1.570571] (scout_base) CommandEnded: {'returncode': 0}
[1.571873] (rslidar_sdk) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target rslidar_sdk_node\x1b[0m\n'}
[1.577744] (scout_base) JobEnded: {'identifier': 'scout_base', 'rc': 0}
[1.585152] (rslidar_sdk) StdoutLine: {'line': b'[100%] Built target rslidar_sdk_node\n'}
[1.592843] (rslidar_sdk) CommandEnded: {'returncode': 0}
[1.593496] (rslidar_sdk) JobProgress: {'identifier': 'rslidar_sdk', 'progress': 'install'}
[1.593861] (rslidar_sdk) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/ros_ws/build/rslidar_sdk'], 'cwd': '/home/<USER>/ros_ws/build/rslidar_sdk', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'arduonboard'), ('SSH_CLIENT', '************* 53444 22'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/ros_ws/install/rslidar_msg/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/helpers/browser.sh'), ('MOTD_SHOWN', 'pam'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('OLDPWD', '/home/<USER>/ros_ws/src'), ('TERM_PROGRAM_VERSION', '1.101.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/1000/vscode-ipc-a649209c-24fe-4649-98b4-d7139b622166.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('_CE_M', ''), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'arduonboard'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '412'), ('_CE_CONDA', ''), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/.vscode-server/cli/servers/Stable-2901c5ac6db8a986a5666c3af51ff804d05af0d4/server/bin/remote-cli:/home/<USER>/miniforge3/condabin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'), ('PAPERSIZE', 'a4'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('MAMBA_ROOT_PREFIX', '/home/<USER>/miniforge3'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-bc3405db40dc0fb3.txt'), ('LANG', 'zh_CN.UTF-8'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-52fd21289e.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniforge3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('MAMBA_EXE', '/home/<USER>/miniforge3/bin/mamba'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/ros_ws/build/rslidar_sdk'), ('CONDA_EXE', '/home/<USER>/miniforge3/bin/conda'), ('SSH_CONNECTION', '************* 53444 ************* 22'), ('XDG_DATA_DIRS', '/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble')]), 'shell': False}
[1.600447] (rslidar_sdk) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[1.600731] (rslidar_sdk) StdoutLine: {'line': b'-- Execute custom install script\n'}
[1.600866] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node\n'}
[1.600943] (-) TimerEvent: {}
[1.601061] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py\n'}
[1.601194] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch\n'}
[1.601264] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py\n'}
[1.601315] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz\n'}
[1.601362] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz\n'}
[1.601410] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk\n'}
[1.601452] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk\n'}
[1.601509] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh\n'}
[1.601568] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv\n'}
[1.601626] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh\n'}
[1.601684] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv\n'}
[1.601740] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash\n'}
[1.601796] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh\n'}
[1.601850] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh\n'}
[1.601902] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv\n'}
[1.601943] (rslidar_sdk) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv\n'}
[1.607260] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk\n'}
[1.607381] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake\n'}
[1.607414] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake\n'}
[1.607454] (rslidar_sdk) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml\n'}
[1.608664] (rslidar_sdk) CommandEnded: {'returncode': 0}
[1.614649] (rslidar_sdk) JobEnded: {'identifier': 'rslidar_sdk', 'rc': 0}
[1.615075] (-) EventReactorShutdown: {}
