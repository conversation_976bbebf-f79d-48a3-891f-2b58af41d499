[0.141s] Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_description
[0.152s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.249s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.296s] -- Configuring done
[0.298s] -- Generating done
[0.298s] -- Build files have been written to: /home/<USER>/ros_ws/build/scout_description
[0.300s] Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_description
[0.302s] Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
[0.335s] Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
[0.337s] Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
[0.344s] -- Install configuration: ""
[0.345s] -- Execute custom install script
[0.345s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//launch/scout_base_description.launch.py
[0.345s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/generate_urdf.sh
[0.345s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/scout_v2.urdf
[0.345s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/scout_v2.xacro
[0.345s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/scout_wheel_type1.xacro
[0.345s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//urdf/scout_wheel_type2.xacro
[0.345s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/base_link.dae
[0.345s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/base_link_full.dae
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/hokuyo.dae
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/wheel_type1.dae
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description//meshes/wheel_type2.dae
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/ament_index/resource_index/package_run_dependencies/scout_description
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/ament_index/resource_index/parent_prefix_path/scout_description
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/environment/ament_prefix_path.sh
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/environment/ament_prefix_path.dsv
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/environment/path.sh
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/environment/path.dsv
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/local_setup.bash
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/local_setup.sh
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/local_setup.zsh
[0.346s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/local_setup.dsv
[0.347s] -- Symlinking: /home/<USER>/ros_ws/install/scout_description/share/scout_description/package.dsv
[0.354s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/ament_index/resource_index/packages/scout_description
[0.355s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/cmake/scout_descriptionConfig.cmake
[0.355s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/cmake/scout_descriptionConfig-version.cmake
[0.355s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_description/share/scout_description/package.xml
[0.356s] Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
