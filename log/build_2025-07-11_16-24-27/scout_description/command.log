Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_description
Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_description
Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
