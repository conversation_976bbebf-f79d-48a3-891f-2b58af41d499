[0.052s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.052s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x76fab1ffaec0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x76fab21d4520>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x76fab21d4520>>)
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.127s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.127s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros_ws'
[0.127s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.128s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ignore', 'ignore_ament_install']
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore_ament_install'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_pkg']
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_pkg'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_meta']
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_meta'
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ros']
[0.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ros'
[0.138s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ardupilot_mavros_bridge' with type 'ros.ament_python' and name 'ardupilot_mavros_bridge'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ignore', 'ignore_ament_install']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore_ament_install'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_pkg']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_pkg'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_meta']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_meta'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ros']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ros'
[0.138s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_msg' with type 'ros.ament_cmake' and name 'rslidar_msg'
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.138s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore'
[0.139s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore_ament_install'
[0.139s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_pkg']
[0.139s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_pkg'
[0.139s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_meta']
[0.139s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_meta'
[0.139s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ros']
[0.139s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ros'
[0.142s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_sdk' with type 'ros.ament_cmake' and name 'rslidar_sdk'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'ignore_ament_install'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['colcon_pkg']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'colcon_pkg'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['colcon_meta']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'colcon_meta'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['ros']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'ros'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['cmake', 'python']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'cmake'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'python'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['python_setup_py']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'python_setup_py'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'ignore_ament_install'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['colcon_pkg']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'colcon_pkg'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['colcon_meta']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'colcon_meta'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['ros']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'ros'
[0.143s] DEBUG:colcon.colcon_core.package_identification:Package 'src/scout_ros2/scout_base' with type 'ros.ament_cmake' and name 'scout_base'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['ignore', 'ignore_ament_install']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'ignore'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'ignore_ament_install'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['colcon_pkg']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'colcon_pkg'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['colcon_meta']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'colcon_meta'
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['ros']
[0.143s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'ros'
[0.144s] DEBUG:colcon.colcon_core.package_identification:Package 'src/scout_ros2/scout_description' with type 'ros.ament_cmake' and name 'scout_description'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'colcon_pkg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['colcon_meta']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'colcon_meta'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['ros']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'ros'
[0.144s] DEBUG:colcon.colcon_core.package_identification:Package 'src/scout_ros2/scout_msgs' with type 'ros.ament_cmake' and name 'scout_msgs'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'colcon_pkg'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['colcon_meta']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'colcon_meta'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['ros']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'ros'
[0.145s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ugv_sdk' with type 'ros.catkin' and name 'ugv_sdk'
[0.145s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.145s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.145s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.145s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.145s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.164s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.164s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.165s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 324 installed packages in /opt/ros/humble
[0.166s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.186s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_first' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_force_configure' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'ament_cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.186s] DEBUG:colcon.colcon_core.verb:Building package 'ardupilot_mavros_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge', 'symlink_install': False, 'test_result_base': None}
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_cache' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_first' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_force_configure' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'ament_cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.186s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_msg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_msg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_msg', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_msg', 'symlink_install': False, 'test_result_base': None}
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_target' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_clean_first' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_force_configure' from command line to 'False'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'ament_cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.186s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.186s] DEBUG:colcon.colcon_core.verb:Building package 'scout_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/scout_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/scout_description', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/scout_ros2/scout_description', 'symlink_install': False, 'test_result_base': None}
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_target' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.187s] DEBUG:colcon.colcon_core.verb:Building package 'scout_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/scout_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/scout_msgs', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs', 'symlink_install': False, 'test_result_base': None}
[0.187s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_target' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.187s] DEBUG:colcon.colcon_core.verb:Building package 'ugv_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/ugv_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/ugv_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/ugv_sdk', 'symlink_install': False, 'test_result_base': None}
[0.187s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.187s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_sdk', 'symlink_install': False, 'test_result_base': None}
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_target' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_clean_cache' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_clean_first' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_force_configure' from command line to 'False'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'ament_cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'catkin_cmake_args' from command line to 'None'
[0.187s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.187s] DEBUG:colcon.colcon_core.verb:Building package 'scout_base' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/scout_base', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/scout_base', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/scout_ros2/scout_base', 'symlink_install': False, 'test_result_base': None}
[0.188s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.188s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.188s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_msg' with build type 'ament_cmake'
[0.188s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_msg'
[0.190s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.190s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.190s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.191s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs' with build type 'ament_cmake'
[0.191s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs'
[0.192s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.192s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.193s] INFO:colcon.colcon_ros.task.catkin.build:Building ROS package in '/home/<USER>/ros_ws/src/ugv_sdk' with build type 'catkin'
[0.193s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/ugv_sdk'
[0.193s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.193s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.194s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' with build type 'ament_python'
[0.195s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'ament_prefix_path')
[0.195s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.ps1'
[0.195s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.dsv'
[0.195s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.sh'
[0.196s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.196s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.197s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/scout_ros2/scout_description' with build type 'ament_cmake'
[0.197s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/scout_ros2/scout_description'
[0.197s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.197s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.201s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[0.203s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_msgs
[0.204s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake /home/<USER>/ros_ws/src/ugv_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/ugv_sdk
[0.332s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge'
[0.332s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.332s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.338s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_description
[0.339s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake /home/<USER>/ros_ws/src/ugv_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/ugv_sdk
[0.341s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake --build /home/<USER>/ros_ws/build/ugv_sdk -- -j16 -l16
[0.447s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake --build /home/<USER>/ros_ws/build/ugv_sdk -- -j16 -l16
[0.453s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake --install /home/<USER>/ros_ws/build/ugv_sdk
[0.460s] Level 1:colcon.colcon_core.shell:create_environment_hook('ugv_sdk', 'ros_package_path')
[0.460s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/ros_package_path.ps1'
[0.460s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/ros_package_path.dsv'
[0.461s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/ros_package_path.sh'
[0.461s] Level 1:colcon.colcon_core.shell:create_environment_hook('ugv_sdk', 'pkg_config_path')
[0.461s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path.ps1'
[0.462s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path.dsv'
[0.462s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path.sh'
[0.464s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake --install /home/<USER>/ros_ws/build/ugv_sdk
[0.464s] Level 1:colcon.colcon_core.shell:create_environment_hook('ugv_sdk', 'pkg_config_path_multiarch')
[0.464s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path_multiarch.ps1'
[0.465s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path_multiarch.dsv'
[0.465s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/pkg_config_path_multiarch.sh'
[0.466s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ugv_sdk)
[0.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk' for CMake module files
[0.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk' for CMake config files
[0.469s] Level 1:colcon.colcon_core.shell:create_environment_hook('ugv_sdk', 'cmake_prefix_path')
[0.469s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/cmake_prefix_path.ps1'
[0.469s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/cmake_prefix_path.dsv'
[0.470s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/hook/cmake_prefix_path.sh'
[0.470s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/lib'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/bin'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig/ugv_sdk.pc'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/lib/python3.10/site-packages'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ugv_sdk/bin'
[0.471s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.ps1'
[0.472s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.dsv'
[0.472s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.sh'
[0.473s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.bash'
[0.473s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ugv_sdk/share/ugv_sdk/package.zsh'
[0.474s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/ugv_sdk/share/colcon-core/packages/ugv_sdk)
[0.498s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_description -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_description
[0.499s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
[0.533s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
[0.535s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
[0.541s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --uninstall --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
[0.553s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_description)
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake module files
[0.554s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake config files
[0.554s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_description', 'cmake_prefix_path')
[0.554s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.ps1'
[0.555s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.dsv'
[0.555s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.sh'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/pkgconfig/scout_description.pc'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/python3.10/site-packages'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[0.557s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.ps1'
[0.557s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.dsv'
[0.557s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.sh'
[0.558s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.bash'
[0.558s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.zsh'
[0.558s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_description/share/colcon-core/packages/scout_description)
[0.559s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_description)
[0.559s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake module files
[0.559s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake config files
[0.559s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_description', 'cmake_prefix_path')
[0.559s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.ps1'
[0.559s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.dsv'
[0.559s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.sh'
[0.560s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[0.560s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/pkgconfig/scout_description.pc'
[0.560s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/python3.10/site-packages'
[0.560s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[0.560s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.ps1'
[0.560s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.dsv'
[0.560s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.sh'
[0.561s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.bash'
[0.561s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.zsh'
[0.561s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_description/share/colcon-core/packages/scout_description)
[0.762s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --uninstall --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
[0.763s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data --force
[0.923s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake module files
[0.924s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data --force
[0.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake config files
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib'
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/pkgconfig/ardupilot_mavros_bridge.pc'
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages'
[0.925s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'pythonpath')
[0.925s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.ps1'
[0.926s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.dsv'
[0.927s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.sh'
[0.927s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.927s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ardupilot_mavros_bridge)
[0.928s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.ps1'
[0.928s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.dsv'
[0.929s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.sh'
[0.929s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.bash'
[0.929s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.zsh'
[0.930s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/colcon-core/packages/ardupilot_mavros_bridge)
[0.994s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_msgs' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_msgs -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_msgs
[0.996s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_msgs -- -j16 -l16
[1.119s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[1.120s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[1.220s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_msgs' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_msgs -- -j16 -l16
[1.222s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_msgs
[1.268s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_msgs)
[1.268s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs' for CMake module files
[1.268s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_msgs' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_msgs
[1.269s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs' for CMake config files
[1.269s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_msgs', 'cmake_prefix_path')
[1.269s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.ps1'
[1.269s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.dsv'
[1.270s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.sh'
[1.270s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib'
[1.270s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_msgs', 'ld_library_path_lib')
[1.270s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.ps1'
[1.270s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.dsv'
[1.270s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.sh'
[1.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/bin'
[1.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib/pkgconfig/scout_msgs.pc'
[1.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib/python3.10/site-packages'
[1.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/bin'
[1.271s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.ps1'
[1.271s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.dsv'
[1.272s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.sh'
[1.272s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.bash'
[1.272s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.zsh'
[1.272s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_msgs/share/colcon-core/packages/scout_msgs)
[1.272s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_msgs)
[1.272s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs' for CMake module files
[1.273s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs' for CMake config files
[1.273s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_msgs', 'cmake_prefix_path')
[1.273s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.ps1'
[1.273s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.dsv'
[1.273s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/cmake_prefix_path.sh'
[1.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib'
[1.274s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_msgs', 'ld_library_path_lib')
[1.274s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.ps1'
[1.274s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.dsv'
[1.274s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/hook/ld_library_path_lib.sh'
[1.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/bin'
[1.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib/pkgconfig/scout_msgs.pc'
[1.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/lib/python3.10/site-packages'
[1.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_msgs/bin'
[1.275s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.ps1'
[1.275s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.dsv'
[1.275s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.sh'
[1.275s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.bash'
[1.276s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/package.zsh'
[1.276s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_msgs/share/colcon-core/packages/scout_msgs)
[1.276s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/scout_ros2/scout_base' with build type 'ament_cmake'
[1.276s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/scout_ros2/scout_base'
[1.276s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.276s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.284s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_base -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_base
[1.332s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[1.334s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[1.377s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[1.377s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[1.378s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[1.378s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[1.379s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[1.379s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[1.380s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[1.380s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[1.381s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[1.381s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[1.381s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[1.381s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[1.382s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[1.382s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[1.382s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[1.382s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[1.383s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[1.383s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[1.383s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[1.384s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[1.384s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[1.385s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[1.385s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[1.386s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[1.386s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[1.387s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[1.387s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[1.388s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[1.388s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[1.389s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[1.389s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[1.390s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[1.390s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[1.390s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[1.391s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[1.391s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[1.391s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[1.391s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[1.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[1.392s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[1.393s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[1.393s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[1.394s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[1.394s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[1.395s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[1.395s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_sdk' with build type 'ament_cmake'
[1.395s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_sdk'
[1.395s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.396s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.404s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[1.679s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_base -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_base
[1.681s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
[1.728s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[1.729s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[1.743s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
[1.744s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
[1.759s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_base)
[1.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base' for CMake module files
[1.760s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
[1.760s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base' for CMake config files
[1.761s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_base', 'cmake_prefix_path')
[1.761s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.ps1'
[1.761s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.dsv'
[1.761s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.sh'
[1.762s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib'
[1.762s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/bin'
[1.762s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib/pkgconfig/scout_base.pc'
[1.762s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib/python3.10/site-packages'
[1.762s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/bin'
[1.762s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.ps1'
[1.762s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.dsv'
[1.763s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.sh'
[1.763s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.bash'
[1.763s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.zsh'
[1.763s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_base/share/colcon-core/packages/scout_base)
[1.763s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_base)
[1.763s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base' for CMake module files
[1.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base' for CMake config files
[1.764s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_base', 'cmake_prefix_path')
[1.764s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.ps1'
[1.764s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.dsv'
[1.764s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_base/share/scout_base/hook/cmake_prefix_path.sh'
[1.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib'
[1.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/bin'
[1.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib/pkgconfig/scout_base.pc'
[1.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/lib/python3.10/site-packages'
[1.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_base/bin'
[1.765s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.ps1'
[1.765s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.dsv'
[1.765s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.sh'
[1.766s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.bash'
[1.766s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_base/share/scout_base/package.zsh'
[1.766s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_base/share/colcon-core/packages/scout_base)
[1.782s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[1.783s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[1.797s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[1.797s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[1.798s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[1.798s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[1.798s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[1.798s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[1.798s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[1.799s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[1.799s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[1.799s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[1.799s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[1.799s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[1.799s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[1.799s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[1.799s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[1.800s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[1.800s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[1.800s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[1.800s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[1.800s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[1.801s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[1.801s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[1.801s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[1.801s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[1.801s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[1.801s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[1.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[1.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[1.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[1.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[1.802s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[1.802s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[1.802s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[1.802s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[1.803s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[1.803s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[1.803s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[1.803s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.803s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.803s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.803s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.806s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.806s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.806s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.814s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.814s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.ps1'
[1.815s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_ps1.py'
[1.815s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.ps1'
[1.816s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.sh'
[1.816s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_sh.py'
[1.816s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.sh'
[1.817s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.bash'
[1.817s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.bash'
[1.817s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.zsh'
[1.818s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.zsh'
