[0.007s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[0.015s] [0m=============================================================[0m
[0.015s] [0m-- POINT_TYPE is XYZI[0m
[0.015s] [0m=============================================================[0m
[0.021s] [0m=============================================================[0m
[0.021s] [0m-- ROS Not Found. ROS Support is turned Off.[0m
[0.021s] [0m=============================================================[0m
[0.104s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.129s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.130s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.134s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.141s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.148s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.165s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.166s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.224s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.251s] [0m=============================================================[0m
[0.251s] [0m-- ROS2 Found. ROS2 Support is turned On.[0m
[0.251s] [0m=============================================================[0m
[0.251s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.267s] -- Found rslidar_msg: 0.0.0 (/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/cmake)
[0.276s] [0m=============================================================[0m
[0.276s] [0m-- CMake run for UNIX GNU Compiler[0m
[0.276s] [0m=============================================================[0m
[0.276s] [0m=============================================================[0m
[0.276s] [0m-- rs_driver Version : v1.5.17[0m
[0.276s] [0m=============================================================[0m
[0.319s] -- Configuring done
[0.324s] -- Generating done
[0.326s] -- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_sdk
[0.332s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
[0.333s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.364s] [35m[1mConsolidate compiler generated dependencies of target rslidar_sdk_node[0m
[0.377s] [100%] Built target rslidar_sdk_node
[0.385s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.387s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.393s] -- Install configuration: "Release"
[0.393s] -- Execute custom install script
[0.393s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node
[0.393s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py
[0.393s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch
[0.393s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh
[0.394s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv
[0.394s] -- Symlinking: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv
[0.400s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk
[0.400s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake
[0.400s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake
[0.400s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml
[0.401s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
