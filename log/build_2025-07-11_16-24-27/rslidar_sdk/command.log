Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_sdk -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_sdk
Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/opt/ros/humble LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/rslidar_msg/lib:${LD_LIBRARY_PATH} PYTHONPATH=/home/<USER>/ros_ws/install/rslidar_msg/local/lib/python3.10/dist-packages:${PYTHONPATH} /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
