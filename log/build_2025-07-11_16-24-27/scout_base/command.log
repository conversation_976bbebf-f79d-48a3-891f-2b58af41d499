Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_base -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_base
Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_base -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_base
Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
