[0.007s] Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_base -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_base
[0.016s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.090s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.093s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.109s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.111s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.115s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.122s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.135s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.163s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.163s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.226s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.258s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.269s] -- Found tf2_geometry_msgs: 0.25.13 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[0.272s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.273s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.317s] -- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
[0.326s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.335s] -- Found scout_msgs: 0.1.0 (/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake)
[0.345s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.385s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.385s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.386s] -- Configured cppcheck include dirs: /home/<USER>/ros_ws/src/scout_ros2/scout_base/include;/home/<USER>/ros_ws/src/scout_ros2/scout_base/$<BUILD_INTERFACE:/opt/ros/humble/include/tf2_geometry_msgs
[0.386s] -- Configured cppcheck exclude dirs and/or files: 
[0.386s] -- Added test 'cpplint' to check C / C++ code against the Google style
[0.386s] -- Configured cpplint exclude dirs and/or files: 
[0.386s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.386s] -- Added test 'lint_cmake' to check CMake code style
[0.387s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.387s] -- Added test 'uncrustify' to check C / C++ code style
[0.387s] -- Configured uncrustify additional arguments: 
[0.387s] -- Added test 'xmllint' to check XML markup files
[0.388s] -- Configuring done
[0.394s] -- Generating done
[0.397s] -- Build files have been written to: /home/<USER>/ros_ws/build/scout_base
[0.402s] Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_base -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_base
[0.404s] Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
[0.439s] [35m[1mConsolidate compiler generated dependencies of target scout_base_node[0m
[0.457s] [100%] Built target scout_base_node
[0.466s] Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_base -- -j16 -l16
[0.467s] Invoking command in '/home/<USER>/ros_ws/build/scout_base': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
[0.472s] -- Install configuration: ""
[0.472s] -- Execute custom install script
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/lib/scout_base/scout_base_node
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_base.launch.py
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_mini_base.launch.py
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_mini_omni_base.launch.py
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/package_run_dependencies/scout_base
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/parent_prefix_path/scout_base
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/ament_prefix_path.sh
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/ament_prefix_path.dsv
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/path.sh
[0.473s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/path.dsv
[0.474s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.bash
[0.474s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.sh
[0.474s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.zsh
[0.474s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.dsv
[0.474s] -- Symlinking: /home/<USER>/ros_ws/install/scout_base/share/scout_base/package.dsv
[0.480s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/packages/scout_base
[0.481s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/cmake/scout_baseConfig.cmake
[0.481s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/cmake/scout_baseConfig-version.cmake
[0.481s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/package.xml
[0.483s] Invoked command in '/home/<USER>/ros_ws/build/scout_base' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ros_ws/install/scout_msgs:${AMENT_PREFIX_PATH} CMAKE_PREFIX_PATH=/opt/ros/humble:/home/<USER>/ros_ws/install/ugv_sdk:/home/<USER>/ros_ws/install/scout_msgs LD_LIBRARY_PATH=/home/<USER>/ros_ws/install/scout_msgs/lib:${LD_LIBRARY_PATH} PKG_CONFIG_PATH=/home/<USER>/ros_ws/install/ugv_sdk/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/ros_ws/install/ugv_sdk/lib/pkgconfig PYTHONPATH=/home/<USER>/ros_ws/install/scout_msgs/local/lib/python3.10/dist-packages:${PYTHONPATH} ROS_PACKAGE_PATH=/home/<USER>/ros_ws/install/ugv_sdk/share /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_base
