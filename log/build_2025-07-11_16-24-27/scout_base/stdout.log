-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found tf2_geometry_msgs: 0.25.13 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found scout_msgs: 0.1.0 (/home/<USER>/ros_ws/install/scout_msgs/share/scout_msgs/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/ros_ws/src/scout_ros2/scout_base/include;/home/<USER>/ros_ws/src/scout_ros2/scout_base/$<BUILD_INTERFACE:/opt/ros/humble/include/tf2_geometry_msgs
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros_ws/build/scout_base
[35m[1mConsolidate compiler generated dependencies of target scout_base_node[0m
[100%] Built target scout_base_node
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/lib/scout_base/scout_base_node
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_base.launch.py
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_mini_base.launch.py
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base//launch/scout_mini_omni_base.launch.py
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/package_run_dependencies/scout_base
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/parent_prefix_path/scout_base
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/path.sh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.bash
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.sh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/local_setup.dsv
-- Symlinking: /home/<USER>/ros_ws/install/scout_base/share/scout_base/package.dsv
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/ament_index/resource_index/packages/scout_base
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/cmake/scout_baseConfig.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/cmake/scout_baseConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/ros_ws/install/scout_base/share/scout_base/package.xml
