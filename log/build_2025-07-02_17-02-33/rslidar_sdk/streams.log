[0.007s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.052s] [100%] Built target rslidar_sdk_node
[0.063s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.063s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.070s] -- Install configuration: "Release"
[0.071s] -- Execute custom install script
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/lib/rslidar_sdk/rslidar_sdk_node
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/elequent_start.py
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.launch
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/launch/start.py
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz.rviz
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/rviz/rviz2.rviz
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/package_run_dependencies/rslidar_sdk
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/parent_prefix_path/rslidar_sdk
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.sh
[0.071s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/ament_prefix_path.dsv
[0.072s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.sh
[0.072s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/environment/path.dsv
[0.072s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.bash
[0.072s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.sh
[0.072s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.zsh
[0.072s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/local_setup.dsv
[0.072s] -- Symlinking: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv
[0.077s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/ament_index/resource_index/packages/rslidar_sdk
[0.077s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig.cmake
[0.077s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/cmake/rslidar_sdkConfig-version.cmake
[0.077s] -- Up-to-date symlink: /home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.xml
[0.078s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
