[0.328s] Invoking command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build --no-deps symlink_data
[0.454s] running develop
[0.521s] running egg_info
[0.521s] writing ardupilot_mavros_bridge.egg-info/PKG-INFO
[0.521s] writing dependency_links to ardupilot_mavros_bridge.egg-info/dependency_links.txt
[0.521s] writing entry points to ardupilot_mavros_bridge.egg-info/entry_points.txt
[0.521s] writing requirements to ardupilot_mavros_bridge.egg-info/requires.txt
[0.521s] writing top-level names to ardupilot_mavros_bridge.egg-info/top_level.txt
[0.522s] reading manifest file 'ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.522s] writing manifest file 'ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.523s] running build_ext
[0.523s] Creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot-mavros-bridge.egg-link (link to .)
[0.523s] Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
[0.524s] Installing mission_control_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
[0.524s] 
[0.524s] Installed /home/<USER>/ros_ws/build/ardupilot_mavros_bridge
[0.524s] running symlink_data
[0.541s] Invoked command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build --no-deps symlink_data
