[0.058s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--symlink-install']
[0.059s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x72952152bd60>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7295216d9510>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7295216d9510>>)
[0.134s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.134s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.134s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.134s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.134s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.134s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.134s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros_ws'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.135s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.141s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ignore', 'ignore_ament_install']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore_ament_install'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_pkg']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_pkg'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_meta']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_meta'
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ros']
[0.142s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ros'
[0.144s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ardupilot_mavros_bridge' with type 'ros.ament_python' and name 'ardupilot_mavros_bridge'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore_ament_install'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_pkg']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_pkg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_meta']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_meta'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ros']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ros'
[0.144s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_msg' with type 'ros.ament_cmake' and name 'rslidar_msg'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore'
[0.144s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore_ament_install'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_pkg']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_pkg'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_meta']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_meta'
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ros']
[0.145s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ros'
[0.148s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_sdk' with type 'ros.ament_cmake' and name 'rslidar_sdk'
[0.148s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.148s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.148s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.148s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.148s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.165s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.166s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 3 installed packages in /home/<USER>/ros_ws/install
[0.167s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 324 installed packages in /opt/ros/humble
[0.168s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.188s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_args' from command line to 'None'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target' from command line to 'None'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_first' from command line to 'False'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_force_configure' from command line to 'False'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'ament_cmake_args' from command line to 'None'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.188s] DEBUG:colcon.colcon_core.verb:Building package 'ardupilot_mavros_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge', 'symlink_install': True, 'test_result_base': None}
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_args' from command line to 'None'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target' from command line to 'None'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_cache' from command line to 'False'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_first' from command line to 'False'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_force_configure' from command line to 'False'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'ament_cmake_args' from command line to 'None'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_cmake_args' from command line to 'None'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.188s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_msg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_msg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_msg', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_msg', 'symlink_install': True, 'test_result_base': None}
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_args' from command line to 'None'
[0.188s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target' from command line to 'None'
[0.189s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.189s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.189s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.189s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.189s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.189s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.189s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.189s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_sdk', 'symlink_install': True, 'test_result_base': None}
[0.189s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.190s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.190s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_msg' with build type 'ament_cmake'
[0.190s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_msg'
[0.191s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.191s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.191s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.193s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' with build type 'ament_python'
[0.193s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'ament_prefix_path')
[0.193s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.ps1'
[0.193s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.dsv'
[0.194s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.sh'
[0.194s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.194s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.198s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.344s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge'
[0.344s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.344s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.400s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[0.409s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[0.448s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[0.448s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_msg
[0.449s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[0.449s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[0.450s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[0.450s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[0.450s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[0.450s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[0.451s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[0.451s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[0.451s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[0.451s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[0.452s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[0.452s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.452s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[0.452s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[0.453s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[0.453s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[0.453s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[0.454s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[0.454s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_msg)
[0.454s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake module files
[0.454s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg' for CMake config files
[0.455s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'cmake_prefix_path')
[0.455s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.ps1'
[0.455s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.dsv'
[0.455s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/cmake_prefix_path.sh'
[0.455s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib'
[0.455s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_msg', 'ld_library_path_lib')
[0.455s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.ps1'
[0.456s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.dsv'
[0.456s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/hook/ld_library_path_lib.sh'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/pkgconfig/rslidar_msg.pc'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/lib/python3.10/site-packages'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_msg/bin'
[0.456s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.ps1'
[0.457s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.dsv'
[0.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.sh'
[0.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.bash'
[0.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_msg/share/rslidar_msg/package.zsh'
[0.457s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_msg/share/colcon-core/packages/rslidar_msg)
[0.458s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_sdk' with build type 'ament_cmake'
[0.458s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_sdk'
[0.458s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.458s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.465s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.521s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build --no-deps symlink_data
[0.521s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_sdk -- -j16 -l16
[0.522s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_sdk': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.536s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[0.537s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_sdk' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ros_ws/install/rslidar_msg:/home/<USER>/ros_ws/install/rslidar_sdk:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/rslidar_sdk
[0.537s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[0.538s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[0.538s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[0.538s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[0.539s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[0.539s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.540s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[0.540s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[0.541s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[0.541s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[0.541s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[0.541s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[0.541s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rslidar_sdk)
[0.541s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake module files
[0.542s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk' for CMake config files
[0.542s] Level 1:colcon.colcon_core.shell:create_environment_hook('rslidar_sdk', 'cmake_prefix_path')
[0.542s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.ps1'
[0.542s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.dsv'
[0.542s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/hook/cmake_prefix_path.sh'
[0.542s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib'
[0.543s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.543s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/pkgconfig/rslidar_sdk.pc'
[0.543s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/lib/python3.10/site-packages'
[0.543s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/rslidar_sdk/bin'
[0.543s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.ps1'
[0.543s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.dsv'
[0.543s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.sh'
[0.544s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.bash'
[0.544s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/rslidar_sdk/share/rslidar_sdk/package.zsh'
[0.544s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/rslidar_sdk/share/colcon-core/packages/rslidar_sdk)
[0.734s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'pythonpath_develop')
[0.734s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath_develop.ps1'
[0.734s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build --no-deps symlink_data
[0.735s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath_develop.dsv'
[0.735s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath_develop.sh'
[0.735s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake module files
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake config files
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib'
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/pkgconfig/ardupilot_mavros_bridge.pc'
[0.736s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages'
[0.736s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'pythonpath')
[0.736s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.ps1'
[0.737s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.dsv'
[0.737s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.sh'
[0.737s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[0.737s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ardupilot_mavros_bridge)
[0.737s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.ps1'
[0.737s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.dsv'
[0.738s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.sh'
[0.738s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.bash'
[0.738s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.zsh'
[0.738s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/colcon-core/packages/ardupilot_mavros_bridge)
[0.738s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.739s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.739s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.739s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.742s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.742s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.742s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.752s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.752s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.ps1'
[0.754s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_ps1.py'
[0.755s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.ps1'
[0.757s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.sh'
[0.757s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ros_ws/install/_local_setup_util_sh.py'
[0.758s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.sh'
[0.759s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.bash'
[0.759s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.bash'
[0.760s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ros_ws/install/local_setup.zsh'
[0.760s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ros_ws/install/setup.zsh'
