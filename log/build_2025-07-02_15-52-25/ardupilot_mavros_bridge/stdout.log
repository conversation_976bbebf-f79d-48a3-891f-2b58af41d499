running develop
Removing /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot-mavros-bridge.egg-link (link to .)
running egg_info
writing ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/PKG-INFO
writing dependency_links to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/dependency_links.txt
writing entry points to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/entry_points.txt
writing requirements to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/requires.txt
writing top-level names to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/top_level.txt
reading manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
running build
running build_py
creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib
creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
copying ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
copying ardupilot_mavros_bridge/mission_control_node.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
copying ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
running install
running install_lib
creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/mission_control_node.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/__init__.py to __init__.cpython-310.pyc
byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/mission_control_node.py to mission_control_node.cpython-310.pyc
byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/mavros_bridge_node.py to mavros_bridge_node.cpython-310.pyc
running install_data
copying resource/ardupilot_mavros_bridge -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ament_index/resource_index/packages
copying package.xml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge
copying launch/apm.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
copying launch/mavros_bridge.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
copying launch/complete_system.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
copying launch/navigation_system.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
copying config/mavros_apm.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
copying config/apm_config.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
copying config/apm_pluginlists.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
copying config/nav2_params.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
running install_egg_info
Copying ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info
running install_scripts
Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
Installing mission_control_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
writing list of installed files to '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log'
