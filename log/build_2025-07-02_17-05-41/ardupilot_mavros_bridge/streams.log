[0.308s] Invoking command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --uninstall --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
[0.433s] running develop
[0.500s] Removing /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot-mavros-bridge.egg-link (link to .)
[0.517s] Invoked command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --uninstall --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
[0.518s] Invoking command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data --force
[0.635s] running egg_info
[0.635s] writing ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/PKG-INFO
[0.636s] writing dependency_links to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/dependency_links.txt
[0.636s] writing entry points to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/entry_points.txt
[0.636s] writing requirements to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/requires.txt
[0.636s] writing top-level names to ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/top_level.txt
[0.637s] reading manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.637s] writing manifest file '../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info/SOURCES.txt'
[0.637s] running build
[0.637s] running build_py
[0.637s] creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build
[0.637s] creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib
[0.637s] creating /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.637s] copying ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.637s] copying ardupilot_mavros_bridge/mission_control_node.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.637s] copying ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge
[0.637s] running install
[0.638s] running install_lib
[0.638s] creating /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.638s] copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/__init__.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.638s] copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/mission_control_node.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.638s] copying /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build/lib/ardupilot_mavros_bridge/mavros_bridge_node.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge
[0.638s] byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/__init__.py to __init__.cpython-310.pyc
[0.638s] byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/mission_control_node.py to mission_control_node.cpython-310.pyc
[0.640s] byte-compiling /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge/mavros_bridge_node.py to mavros_bridge_node.cpython-310.pyc
[0.641s] running install_data
[0.641s] copying resource/ardupilot_mavros_bridge -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ament_index/resource_index/packages
[0.641s] copying package.xml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge
[0.641s] copying launch/apm.launch -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.641s] copying launch/apm_optimized.launch -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.641s] copying launch/mavros_bridge.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.641s] copying launch/complete_system.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.641s] copying launch/navigation_system.launch.py -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/launch
[0.641s] copying config/apm_config.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.641s] copying config/apm_config_optimized.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.641s] copying config/apm_pluginlists.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.641s] copying config/nav2_params.yaml -> /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/config
[0.641s] running install_egg_info
[0.642s] Copying ../../build/ardupilot_mavros_bridge/ardupilot_mavros_bridge.egg-info to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages/ardupilot_mavros_bridge-0.0.0-py3.10.egg-info
[0.643s] running install_scripts
[0.655s] Installing mavros_bridge_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
[0.655s] Installing mission_control_node script to /home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/ardupilot_mavros_bridge
[0.655s] writing list of installed files to '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log'
[0.672s] Invoked command in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../build/ardupilot_mavros_bridge build --build-base /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build install --record /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/install.log --single-version-externally-managed install_data --force
