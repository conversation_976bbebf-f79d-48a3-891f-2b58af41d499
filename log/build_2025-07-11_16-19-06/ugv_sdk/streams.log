[0.011s] Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake /home/<USER>/ros_ws/src/ugv_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCATKIN_SYMLINK_INSTALL=ON -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/ugv_sdk
[0.230s] -- The C compiler identification is GNU 11.4.0
[0.230s] -- The CXX compiler identification is GNU 11.4.0
[0.230s] -- Detecting C compiler ABI info
[0.231s] -- Detecting C compiler ABI info - done
[0.234s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.234s] -- Detecting C compile features
[0.234s] -- Detecting C compile features - done
[0.238s] -- Detecting CXX compiler ABI info
[0.317s] -- Detecting CXX compiler AB<PERSON> info - done
[0.322s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.323s] -- Detecting CXX compile features
[0.323s] -- Detecting CXX compile features - done
[0.324s] -- Build package with cmake
[0.324s] -- Setting build type to 'Release' as none was specified.
[0.324s] -- Tests will not be built
[0.326s] -- Looking for pthread.h
[0.385s] -- Looking for pthread.h - found
[0.385s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.481s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.484s] -- Found Threads: TRUE  
[0.488s] -- Project will be installed to /home/<USER>/ros_ws/install/ugv_sdk with 'make install'
[0.488s] --  - To install LIB components to /home/<USER>/ros_ws/install/ugv_sdk/lib
[0.488s] --  - To install BIN components to /home/<USER>/ros_ws/install/ugv_sdk/bin
[0.488s] --  - To install INCLUDE components to /home/<USER>/ros_ws/install/ugv_sdk/include
[0.488s] --  - To install CMAKE components to /home/<USER>/ros_ws/install/ugv_sdk/share/cmake/ugv_sdk
[0.494s] -- Configuring done
[0.514s] -- Generating done
[0.514s] [33mCMake Warning:
[0.514s]   Manually-specified variables were not used by the project:
[0.514s] 
[0.514s]     CATKIN_INSTALL_INTO_PREFIX_ROOT
[0.514s]     CATKIN_SYMLINK_INSTALL
[0.514s] 
[0.514s] [0m
[0.515s] -- Build files have been written to: /home/<USER>/ros_ws/build/ugv_sdk
[0.518s] Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake /home/<USER>/ros_ws/src/ugv_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCATKIN_SYMLINK_INSTALL=ON -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/ugv_sdk
[0.519s] Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake --build /home/<USER>/ros_ws/build/ugv_sdk -- -j16 -l16
[0.575s] [ 14%] [32mBuilding CXX object CMakeFiles/ugv_sdk.dir/src/mobile_robot/bunker_robot.cpp.o[0m
[0.575s] [ 14%] [32mBuilding CXX object CMakeFiles/ugv_sdk.dir/src/utilities/protocol_detector.cpp.o[0m
[0.575s] [ 14%] [32mBuilding CXX object CMakeFiles/ugv_sdk.dir/src/mobile_robot/ranger_robot.cpp.o[0m
[0.575s] [ 14%] [32mBuilding CXX object CMakeFiles/ugv_sdk.dir/src/mobile_robot/titan_robot.cpp.o[0m
[0.575s] [ 17%] [32mBuilding CXX object CMakeFiles/ugv_sdk.dir/src/async_port/async_serial.cpp.o[0m
[0.575s] [ 25%] [32mBuilding C object CMakeFiles/ugv_sdk.dir/src/protocol_v2/agilex_msg_parser_v2.c.o[0m
[0.575s] [ 25%] [32mBuilding CXX object CMakeFiles/ugv_sdk.dir/src/mobile_robot/hunter_robot.cpp.o[0m
[0.575s] [ 32%] [32mBuilding CXX object CMakeFiles/ugv_sdk.dir/src/mobile_robot/scout_robot.cpp.o[0m
[0.576s] [ 32%] [32mBuilding CXX object CMakeFiles/ugv_sdk.dir/src/async_port/async_can.cpp.o[0m
[0.577s] [ 35%] [32mBuilding CXX object CMakeFiles/ugv_sdk.dir/src/protocol_v2/protocol_v2_parser.cpp.o[0m
[0.577s] [ 39%] [32mBuilding C object CMakeFiles/ugv_sdk.dir/src/protocol_v1/agilex_msg_parser_v1.c.o[0m
