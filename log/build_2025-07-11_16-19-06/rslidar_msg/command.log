Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
