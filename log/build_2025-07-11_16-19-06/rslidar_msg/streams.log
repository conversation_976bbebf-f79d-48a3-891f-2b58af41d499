[0.012s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[0.232s] -- The C compiler identification is GNU 11.4.0
[0.232s] -- The CXX compiler identification is GNU 11.4.0
[0.232s] -- Detecting C compiler ABI info
[0.232s] -- Detecting C compiler ABI info - done
[0.232s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.233s] -- Detecting C compile features
[0.233s] -- Detecting C compile features - done
[0.233s] -- Detecting CXX compiler AB<PERSON> info
[0.298s] -- Detecting CXX compiler <PERSON><PERSON> info - done
[0.302s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.302s] -- Detecting CXX compile features
[0.303s] -- Detecting CXX compile features - done
[0.304s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.448s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.554s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.570s] -- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
[0.625s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.633s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.644s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.662s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.680s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.711s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.764s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.768s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.944s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.982s] -- Found FastRTPS: /opt/ros/humble/include  
[1.049s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.064s] -- Looking for pthread.h
[1.211s] -- Looking for pthread.h - found
[1.211s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.293s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.295s] -- Found Threads: TRUE  
[1.359s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[1.816s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[2.413s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[2.714s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[2.757s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
[2.759s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[2.807s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
[2.807s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[2.807s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[2.807s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
[2.839s] -- Found PythonExtra: .so  
[2.924s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.941s] -- Added test 'copyright' to check source files copyright and LICENSE
[2.950s] -- Added test 'lint_cmake' to check CMake code style
[2.954s] -- Added test 'xmllint' to check XML markup files
[2.958s] -- Configuring done
[2.996s] -- Generating done
[3.017s] -- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_msg
[3.088s] Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[3.091s] Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
[3.172s] [  3%] [34m[1mGenerating C code for ROS interfaces[0m
[3.205s] [  6%] [34m[1mGenerating C++ code for ROS interfaces[0m
[3.227s] [  6%] Built target ament_cmake_python_symlink_rslidar_msg
[3.478s] running egg_info
[3.479s] creating rslidar_msg.egg-info
[3.479s] writing rslidar_msg.egg-info/PKG-INFO
[3.479s] writing dependency_links to rslidar_msg.egg-info/dependency_links.txt
[3.479s] writing top-level names to rslidar_msg.egg-info/top_level.txt
[3.479s] writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[3.482s] reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[3.482s] writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[3.570s] [  9%] [32mBuilding C object CMakeFiles/rslidar_msg__rosidl_generator_c.dir/rosidl_generator_c/rslidar_msg/msg/detail/rslidar_packet__functions.c.o[0m
[3.574s] [  9%] Built target ament_cmake_python_build_rslidar_msg_egg
[3.658s] [ 12%] [32m[1mLinking C shared library librslidar_msg__rosidl_generator_c.so[0m
[3.706s] [ 12%] Built target rslidar_msg__cpp
[3.741s] [ 16%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[3.759s] [ 19%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[3.770s] [ 22%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[3.776s] [ 22%] Built target rslidar_msg__rosidl_generator_c
[3.818s] [ 25%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[3.818s] [ 32%] [34m[1mGenerating C introspection for ROS interfaces[0m
[3.819s] [ 32%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
