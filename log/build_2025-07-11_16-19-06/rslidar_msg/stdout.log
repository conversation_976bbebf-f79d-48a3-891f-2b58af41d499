-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
-- Found PythonExtra: .so  
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/ros_ws/build/rslidar_msg
[  3%] [34m[1mGenerating C code for ROS interfaces[0m
[  6%] [34m[1mGenerating C++ code for ROS interfaces[0m
[  6%] Built target ament_cmake_python_symlink_rslidar_msg
running egg_info
creating rslidar_msg.egg-info
writing rslidar_msg.egg-info/PKG-INFO
writing dependency_links to rslidar_msg.egg-info/dependency_links.txt
writing top-level names to rslidar_msg.egg-info/top_level.txt
writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'
reading manifest file 'rslidar_msg.egg-info/SOURCES.txt'
writing manifest file 'rslidar_msg.egg-info/SOURCES.txt'
[  9%] [32mBuilding C object CMakeFiles/rslidar_msg__rosidl_generator_c.dir/rosidl_generator_c/rslidar_msg/msg/detail/rslidar_packet__functions.c.o[0m
[  9%] Built target ament_cmake_python_build_rslidar_msg_egg
[ 12%] [32m[1mLinking C shared library librslidar_msg__rosidl_generator_c.so[0m
[ 12%] Built target rslidar_msg__cpp
[ 16%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[ 19%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[ 22%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[ 22%] Built target rslidar_msg__rosidl_generator_c
[ 25%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[ 32%] [34m[1mGenerating C introspection for ROS interfaces[0m
[ 32%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
