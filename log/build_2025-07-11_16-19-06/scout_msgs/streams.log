[0.011s] Invoking command in '/home/<USER>/ros_ws/build/scout_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_msgs -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_msgs
[0.230s] -- The C compiler identification is GNU 11.4.0
[0.231s] -- The CXX compiler identification is GNU 11.4.0
[0.231s] -- Detecting C compiler ABI info
[0.231s] -- Detecting C compiler ABI info - done
[0.231s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.231s] -- Detecting C compile features
[0.231s] -- Detecting C compile features - done
[0.232s] -- Detecting CXX compiler ABI info
[0.292s] -- Detecting CXX compiler AB<PERSON> info - done
[0.298s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.298s] -- Detecting CXX compile features
[0.299s] -- Detecting CXX compile features - done
[0.300s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.428s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.510s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.525s] -- Found builtin_interfaces: 1.2.1 (/opt/ros/humble/share/builtin_interfaces/cmake)
[0.549s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.551s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.556s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.565s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.579s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.598s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[0.607s] -- Found std_msgs: 4.9.0 (/opt/ros/humble/share/std_msgs/cmake)
[1.107s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[1.583s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.809s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.839s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
[1.841s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[1.871s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
[1.871s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.871s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[1.871s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
[1.909s] -- Found PythonExtra: .so  
[1.986s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.024s] -- Added test 'copyright' to check source files copyright and LICENSE
[2.028s] -- Added test 'lint_cmake' to check CMake code style
[2.028s] -- Added test 'xmllint' to check XML markup files
[2.033s] -- Configuring done
[2.091s] -- Generating done
[2.111s] -- Build files have been written to: /home/<USER>/ros_ws/build/scout_msgs
[2.131s] Invoked command in '/home/<USER>/ros_ws/build/scout_msgs' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_msgs -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_msgs
[2.136s] Invoking command in '/home/<USER>/ros_ws/build/scout_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_msgs -- -j16 -l16
[2.210s] [  1%] [34m[1mGenerating C code for ROS interfaces[0m
[2.244s] [  3%] [34m[1mGenerating C++ code for ROS interfaces[0m
[2.244s] [  3%] Built target ament_cmake_python_symlink_scout_msgs
[2.635s] running egg_info
[2.636s] creating scout_msgs.egg-info
[2.636s] writing scout_msgs.egg-info/PKG-INFO
[2.637s] writing dependency_links to scout_msgs.egg-info/dependency_links.txt
[2.637s] writing top-level names to scout_msgs.egg-info/top_level.txt
[2.637s] writing manifest file 'scout_msgs.egg-info/SOURCES.txt'
[2.641s] reading manifest file 'scout_msgs.egg-info/SOURCES.txt'
[2.641s] writing manifest file 'scout_msgs.egg-info/SOURCES.txt'
[2.718s] [  3%] Built target ament_cmake_python_build_scout_msgs_egg
[2.930s] [  3%] Built target scout_msgs__cpp
[2.947s] [  6%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[2.947s] [  6%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[2.949s] [  7%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[3.120s] [  9%] [32mBuilding C object CMakeFiles/scout_msgs__rosidl_generator_c.dir/rosidl_generator_c/scout_msgs/msg/detail/scout_actuator_state__functions.c.o[0m
[3.121s] [ 11%] [32mBuilding C object CMakeFiles/scout_msgs__rosidl_generator_c.dir/rosidl_generator_c/scout_msgs/msg/detail/scout_light_cmd__functions.c.o[0m
[3.127s] [ 14%] [32mBuilding C object CMakeFiles/scout_msgs__rosidl_generator_c.dir/rosidl_generator_c/scout_msgs/msg/detail/scout_light_state__functions.c.o[0m
[3.127s] [ 14%] [32mBuilding C object CMakeFiles/scout_msgs__rosidl_generator_c.dir/rosidl_generator_c/scout_msgs/msg/detail/scout_rc_state__functions.c.o[0m
[3.131s] [ 15%] [32mBuilding C object CMakeFiles/scout_msgs__rosidl_generator_c.dir/rosidl_generator_c/scout_msgs/msg/detail/scout_status__functions.c.o[0m
[3.231s] [ 17%] [32m[1mLinking C shared library libscout_msgs__rosidl_generator_c.so[0m
[3.311s] [ 17%] Built target scout_msgs__rosidl_generator_c
[3.325s] [ 20%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[3.326s] [ 20%] [34m[1mGenerating C introspection for ROS interfaces[0m
[3.329s] [ 22%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[3.625s] [ 26%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/scout_msgs/msg/scout_light_state__type_support.cpp.o[0m
[3.625s] [ 26%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/scout_msgs/msg/scout_light_cmd__type_support.cpp.o[0m
[3.625s] [ 26%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/scout_msgs/msg/scout_actuator_state__type_support.cpp.o[0m
[3.625s] [ 28%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/scout_msgs/msg/scout_status__type_support.cpp.o[0m
[3.628s] [ 30%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/scout_msgs/msg/scout_rc_state__type_support.cpp.o[0m
[3.818s] [ 31%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/scout_msgs/msg/detail/scout_status__type_support.cpp.o[0m
[3.835s] [ 33%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/scout_msgs/msg/detail/scout_light_state__type_support.cpp.o[0m
[3.836s] [ 34%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/scout_msgs/msg/detail/scout_actuator_state__type_support.cpp.o[0m
[3.836s] [ 36%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/scout_msgs/msg/detail/scout_light_cmd__type_support.cpp.o[0m
[3.843s] [ 38%] [32mBuilding CXX object CMakeFiles/scout_msgs__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/scout_msgs/msg/detail/scout_rc_state__type_support.cpp.o[0m
