[0.065s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--symlink-install']
[0.065s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7869d1d9b280>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7869d1fc88e0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7869d1fc88e0>>)
[0.147s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.147s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.147s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.147s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.147s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.147s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.147s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ros_ws'
[0.147s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.148s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ignore', 'ignore_ament_install']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ignore_ament_install'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_pkg']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_pkg'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['colcon_meta']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'colcon_meta'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extensions ['ros']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(src/ardupilot_mavros_bridge) by extension 'ros'
[0.156s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ardupilot_mavros_bridge' with type 'ros.ament_python' and name 'ardupilot_mavros_bridge'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ignore', 'ignore_ament_install']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ignore_ament_install'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_pkg']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_pkg'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['colcon_meta']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'colcon_meta'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extensions ['ros']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_msg) by extension 'ros'
[0.157s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_msg' with type 'ros.ament_cmake' and name 'rslidar_msg'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ignore_ament_install'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_pkg']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_pkg'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['colcon_meta']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'colcon_meta'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extensions ['ros']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(src/rslidar_sdk) by extension 'ros'
[0.160s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rslidar_sdk' with type 'ros.ament_cmake' and name 'rslidar_sdk'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'ignore_ament_install'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['colcon_pkg']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'colcon_pkg'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['colcon_meta']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'colcon_meta'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['ros']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'ros'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['cmake', 'python']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'cmake'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'python'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extensions ['python_setup_py']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2) by extension 'python_setup_py'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['ignore', 'ignore_ament_install']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'ignore'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'ignore_ament_install'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['colcon_pkg']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'colcon_pkg'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['colcon_meta']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_base) by extension 'ros'
[0.161s] DEBUG:colcon.colcon_core.package_identification:Package 'src/scout_ros2/scout_base' with type 'ros.ament_cmake' and name 'scout_base'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['ignore', 'ignore_ament_install']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'ignore'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'ignore_ament_install'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['colcon_pkg']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'colcon_pkg'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['colcon_meta']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extensions ['ros']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_description) by extension 'ros'
[0.162s] DEBUG:colcon.colcon_core.package_identification:Package 'src/scout_ros2/scout_description' with type 'ros.ament_cmake' and name 'scout_description'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'ignore'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'ignore_ament_install'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['colcon_pkg']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'colcon_pkg'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['colcon_meta']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'colcon_meta'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extensions ['ros']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/scout_ros2/scout_msgs) by extension 'ros'
[0.162s] DEBUG:colcon.colcon_core.package_identification:Package 'src/scout_ros2/scout_msgs' with type 'ros.ament_cmake' and name 'scout_msgs'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['ignore', 'ignore_ament_install']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'ignore'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'ignore_ament_install'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['colcon_pkg']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'colcon_pkg'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['colcon_meta']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'colcon_meta'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extensions ['ros']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/ugv_sdk) by extension 'ros'
[0.163s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ugv_sdk' with type 'ros.catkin' and name 'ugv_sdk'
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.181s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.181s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.182s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 324 installed packages in /opt/ros/humble
[0.183s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.209s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_args' from command line to 'None'
[0.209s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target' from command line to 'None'
[0.209s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.209s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[0.209s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_clean_first' from command line to 'False'
[0.209s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'cmake_force_configure' from command line to 'False'
[0.209s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'ament_cmake_args' from command line to 'None'
[0.209s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[0.209s] Level 5:colcon.colcon_core.verb:set package 'ardupilot_mavros_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.209s] DEBUG:colcon.colcon_core.verb:Building package 'ardupilot_mavros_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge', 'symlink_install': True, 'test_result_base': None}
[0.210s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_args' from command line to 'None'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target' from command line to 'None'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_cache' from command line to 'False'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_clean_first' from command line to 'False'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'cmake_force_configure' from command line to 'False'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'ament_cmake_args' from command line to 'None'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_cmake_args' from command line to 'None'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'rslidar_msg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.210s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_msg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_msg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_msg', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_msg', 'symlink_install': True, 'test_result_base': None}
[0.210s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_args' from command line to 'None'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_target' from command line to 'None'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.210s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_clean_first' from command line to 'False'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'cmake_force_configure' from command line to 'False'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'ament_cmake_args' from command line to 'None'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.211s] DEBUG:colcon.colcon_core.verb:Building package 'scout_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/scout_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/scout_description', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/scout_ros2/scout_description', 'symlink_install': True, 'test_result_base': None}
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_args' from command line to 'None'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_target' from command line to 'None'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.211s] Level 5:colcon.colcon_core.verb:set package 'scout_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.211s] DEBUG:colcon.colcon_core.verb:Building package 'scout_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/scout_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/scout_msgs', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs', 'symlink_install': True, 'test_result_base': None}
[0.212s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_args' from command line to 'None'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_target' from command line to 'None'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'ugv_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.212s] DEBUG:colcon.colcon_core.verb:Building package 'ugv_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/ugv_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/ugv_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/ugv_sdk', 'symlink_install': True, 'test_result_base': None}
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_args' from command line to 'None'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target' from command line to 'None'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_cache' from command line to 'False'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_clean_first' from command line to 'False'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'cmake_force_configure' from command line to 'False'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'ament_cmake_args' from command line to 'None'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_cmake_args' from command line to 'None'
[0.212s] Level 5:colcon.colcon_core.verb:set package 'rslidar_sdk' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.212s] DEBUG:colcon.colcon_core.verb:Building package 'rslidar_sdk' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/rslidar_sdk', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/rslidar_sdk', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/rslidar_sdk', 'symlink_install': True, 'test_result_base': None}
[0.213s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_args' from command line to 'None'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_target' from command line to 'None'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_clean_cache' from command line to 'False'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_clean_first' from command line to 'False'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'cmake_force_configure' from command line to 'False'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'ament_cmake_args' from command line to 'None'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'catkin_cmake_args' from command line to 'None'
[0.213s] Level 5:colcon.colcon_core.verb:set package 'scout_base' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.213s] DEBUG:colcon.colcon_core.verb:Building package 'scout_base' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ros_ws/build/scout_base', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ros_ws/install/scout_base', 'merge_install': False, 'path': '/home/<USER>/ros_ws/src/scout_ros2/scout_base', 'symlink_install': True, 'test_result_base': None}
[0.213s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.214s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.215s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/rslidar_msg' with build type 'ament_cmake'
[0.215s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/rslidar_msg'
[0.216s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.216s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.216s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.218s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs' with build type 'ament_cmake'
[0.218s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/scout_ros2/scout_msgs'
[0.218s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.218s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.219s] INFO:colcon.colcon_ros.task.catkin.build:Building ROS package in '/home/<USER>/ros_ws/src/ugv_sdk' with build type 'catkin'
[0.219s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/ugv_sdk'
[0.219s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.219s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.221s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge' with build type 'ament_python'
[0.221s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'ament_prefix_path')
[0.221s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.ps1'
[0.221s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.dsv'
[0.222s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/ament_prefix_path.sh'
[0.222s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.222s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.223s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ros_ws/src/scout_ros2/scout_description' with build type 'ament_cmake'
[0.223s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ros_ws/src/scout_ros2/scout_description'
[0.223s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.223s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.227s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[0.229s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_msgs -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_msgs
[0.230s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake /home/<USER>/ros_ws/src/ugv_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCATKIN_SYMLINK_INSTALL=ON -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/ugv_sdk
[0.441s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ros_ws/src/ardupilot_mavros_bridge'
[0.441s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.441s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.453s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_description -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_description
[0.733s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build --no-deps symlink_data
[0.738s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ugv_sdk' returned '0': /usr/bin/cmake /home/<USER>/ros_ws/src/ugv_sdk -DCATKIN_INSTALL_INTO_PREFIX_ROOT=0 -DCATKIN_SYMLINK_INSTALL=ON -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/ugv_sdk
[0.739s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/ugv_sdk': /usr/bin/cmake --build /home/<USER>/ros_ws/build/ugv_sdk -- -j16 -l16
[1.245s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_description -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_description
[1.252s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
[1.326s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'pythonpath_develop')
[1.327s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge' returned '0': PYTHONPATH=/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py develop --editable --build-directory /home/<USER>/ros_ws/build/ardupilot_mavros_bridge/build --no-deps symlink_data
[1.330s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath_develop.ps1'
[1.332s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath_develop.dsv'
[1.334s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/build/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath_develop.sh'
[1.338s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake module files
[1.340s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge' for CMake config files
[1.341s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib'
[1.341s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[1.341s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/pkgconfig/ardupilot_mavros_bridge.pc'
[1.342s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/lib/python3.10/site-packages'
[1.342s] Level 1:colcon.colcon_core.shell:create_environment_hook('ardupilot_mavros_bridge', 'pythonpath')
[1.342s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.ps1'
[1.343s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.dsv'
[1.344s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/hook/pythonpath.sh'
[1.344s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/bin'
[1.344s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ardupilot_mavros_bridge)
[1.345s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.ps1'
[1.347s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.dsv'
[1.348s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.sh'
[1.350s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.bash'
[1.351s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/ardupilot_mavros_bridge/package.zsh'
[1.353s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/ardupilot_mavros_bridge/share/colcon-core/packages/ardupilot_mavros_bridge)
[1.358s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_description -- -j16 -l16
[1.382s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
[1.418s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_description)
[1.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake module files
[1.419s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/ros_ws/build/scout_description
[1.420s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake config files
[1.420s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_description', 'cmake_prefix_path')
[1.421s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.ps1'
[1.422s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.dsv'
[1.423s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.sh'
[1.424s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[1.424s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/pkgconfig/scout_description.pc'
[1.425s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/python3.10/site-packages'
[1.425s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[1.426s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.ps1'
[1.427s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.dsv'
[1.428s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.sh'
[1.429s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.bash'
[1.430s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.zsh'
[1.431s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_description/share/colcon-core/packages/scout_description)
[1.432s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(scout_description)
[1.432s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake module files
[1.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description' for CMake config files
[1.433s] Level 1:colcon.colcon_core.shell:create_environment_hook('scout_description', 'cmake_prefix_path')
[1.434s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.ps1'
[1.435s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.dsv'
[1.437s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ros_ws/install/scout_description/share/scout_description/hook/cmake_prefix_path.sh'
[1.438s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[1.438s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/pkgconfig/scout_description.pc'
[1.439s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/lib/python3.10/site-packages'
[1.439s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ros_ws/install/scout_description/bin'
[1.440s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.ps1'
[1.441s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.dsv'
[1.443s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.sh'
[1.444s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.bash'
[1.444s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ros_ws/install/scout_description/share/scout_description/package.zsh'
[1.445s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ros_ws/install/scout_description/share/colcon-core/packages/scout_description)
[2.350s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/scout_msgs' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/scout_ros2/scout_msgs -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/scout_msgs
[2.355s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/scout_msgs': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/scout_msgs -- -j16 -l16
[3.303s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ros_ws/build/rslidar_msg' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/ros_ws/src/rslidar_msg -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros_ws/install/rslidar_msg
[3.306s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ros_ws/build/rslidar_msg': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/ros_ws/build/rslidar_msg -- -j16 -l16
